var OK_LANG = 'OK';
var CANCEL_LANG = 'Annuler';
var RESET_LANG = 'Réinitialiser';
var ABOUT_LANG = 'À propos';
var ZOOM_LANG = 'Zoom';
var COPY_LANG = 'Copier';
var BUTTON_TIME_LIST = 'MES<br>TEMPS';
var BUTTON_OPTIONS = 'OPTIONS';
var BUTTON_EXPORT = 'Exporter';
var BUTTON_DONATE = 'FAIRE UN DON';
var PROPERTY_SR = 'Avec la session';
var PROPERTY_USEINS = 'Utiliser l\'inspection WCA';
var PROPERTY_USEINS_STR = 'Toujours (bas)|Toujours (haut)|Sauf BLD (bas)|Sauf BLD (haut)|Jamais';
var PROPERTY_SHOWINS = 'Afficher une icône lorsque l\'inspection est activée';
var PROPERTY_VOICEINS = 'Voix de l\'inspection WCA';
var PROPERTY_VOICEINS_STR = 'Aucune|Voix masculine|Voix féminine';
var PROPERTY_VOICEVOL = 'Volume de la voix d\'inspection';
var PROPERTY_PHASES = 'Multi-phase';
var PROPERTY_TIMERSIZE = 'Taille du chronomètre';
var PROPERTY_USEMILLI = 'Utiliser les millisecondes';
var PROPERTY_SMALLADP = 'Utiliser une petite police pour les décimales';
var PROPERTY_SCRSIZE = 'Taille du mélange';
var PROPERTY_SCRMONO = 'Mélange à espacement fixe';
var PROPERTY_SCRLIM = 'Limiter la taille de la zone de mélange';
var PROPERTY_SCRALIGN = 'Alignement de la zone de mélange';
var PROPERTY_SCRALIGN_STR = 'Centre|Gauche|Droite';
var PROPERTY_SCRWRAP = 'Scramble Wrap';
var PROPERTY_SCRWRAP_STR = 'Balanced|Normal';
var PROPERTY_SCRNEUT = 'Couleur neutre';
var PROPERTY_SCRNEUT_STR = 'Aucun|Simple Face|Double Face|Six Faces';
var PROPERTY_SCREQPR = 'Probabilités des états des mélanges d\'entrainement';
var PROPERTY_SCREQPR_STR = 'Actual|Equal|Random order';
var PROPERTY_SCRFAST = 'Utiliser des mélanges rapides pour 4x4x4 (non officiel)';
var PROPERTY_SCRKEYM = 'Indiquer le(s) mouvement(s) clé(s) dans Mélange';
var PROPERTY_SCRCLK = 'Quand le mélange est cliqué';
var PROPERTY_SCRCLK_STR = 'Rien|Copier|Prochain mélange';
var PROPERTY_WNDSCR = 'Style du panneau Mélange';
var PROPERTY_WNDSTAT = 'Style du panneau Statistiques';
var PROPERTY_WNDTOOL = 'Style du panneau Outils';
var PROPERTY_WND_STR = 'Normal|Plat';
var EXPORT_DATAEXPORT = 'Import/Export des données';
var EXPORT_TOFILE = 'Exporter vers';
var EXPORT_FROMFILE = 'Importer un fichier';
var EXPORT_TOSERV = 'Exporter vers un serveur';
var EXPORT_FROMSERV = 'Importer d\'un serveur';
var EXPORT_FROMOTHER = 'Importer d\'un autre chronomètre';
var EXPORT_USERID = 'Veuillez saisir votre compte (uniquement chiffres ou lettres)';
var EXPORT_INVID = 'Lettres ou chiffres uniquement !';
var EXPORT_ERROR = 'Des erreurs se sont produites...';
var EXPORT_NODATA = 'Aucune donnée trouvée pour votre compte';
var EXPORT_UPLOADED = 'Envoyé avec succès';
var EXPORT_CODEPROMPT = 'Enregistrez ce code, ou entrez un code couleurs préalablement enregistré pour l\'importer.';
var EXPORT_ONLYOPT = 'Exporter/Importer seulement la configuration';
var EXPORT_ACCOUNT = 'Exporter les comptes';
var EXPORT_LOGINGGL = 'S\'enregistrer en utilisant un compte Google';
var EXPORT_LOGINWCA = 'S\'enregistrer en utilisant un compte WCA';
var EXPORT_LOGOUTCFM = 'Confirmez-vous la déconnexion?';
var EXPORT_LOGINAUTHED = 'Autorisé<br>Récupération des données...';
var EXPORT_AEXPALERT = 'Plus de %d résolutions depuis la dernière sauvegarde';
var EXPORT_WHICH = 'Vous avez un(des) fichier(s) %d, lequel doit-être importé ?';
var EXPORT_WHICH_ITEM = '%s résolutions, importées à %t';
var IMPORT_FINAL_CONFIRM = 'L\'import de données remplacera toutes vos données locales ! (%d sessions modifiées, %r résolutions supprimées). Confirmez-vous l\'import ?';
var BUTTON_SCRAMBLE = 'MELA-<br>NGE';
var BUTTON_TOOLS = 'OUTILS';
var IMAGE_UNAVAILABLE = 'Non disponible pour ce type de mélange';
var TOOLS_SELECTFUNC = 'Fonction';
var TOOLS_CROSS = 'Résoudre la croix';
var TOOLS_EOLINE = 'Résoudre l\'EOLine';
var TOOLS_ROUX1 = 'Résoudre 1er bloc Roux';
var TOOLS_222FACE = 'Face de 2x2x2';
var TOOLS_GIIKER = 'Cube Bluetooth';
var TOOLS_IMAGE = 'aperçu du mélange';
var TOOLS_STATS = 'Statistiques';
var TOOLS_HUGESTATS = 'Stats sur plusieurs sessions';
var TOOLS_DISTRIBUTION = 'Répartition des temps';
var TOOLS_TREND = 'Tendance des temps';
var TOOLS_METRONOME = 'Métronome';
var TOOLS_RECONS = 'Reconstruction';
var TOOLS_RECONS_NODATA = 'Il n\'existe pas de solution.';
var TOOLS_RECONS_TITLE = 'insp|exec|turn|tps';
var TOOLS_TRAINSTAT = 'Stats des entraînements';
var TOOLS_BLDHELPER = 'Assistant BLD';
var TOOLS_CFMTIME = 'Confirmation du temps';
var TOOLS_SOLVERS = 'Solveurs';
var TOOLS_DLYSTAT = 'Stats quotidiennes';
var TOOLS_DLYSTAT1 = 'Période|Début de journée|Semaine';
var TOOLS_DLYSTAT_OPT1 = 'jour|semaine|mois|année';
var TOOLS_DLYSTAT_OPT2 = 'Dim|Lun|Mar|Mer|Jeu|Ven|Sam';
var TOOLS_SYNCSEED = 'Mélange classique';
var TOOLS_SYNCSEED_SEED = 'Seed';
var TOOLS_SYNCSEED_INPUT = 'Entrer un Seed';
var TOOLS_SYNCSEED_30S = 'Utiliser un Seed de 30 sec';
var TOOLS_SYNCSEED_HELP = 'Si activé, le mélange dépendra uniquement du seed et des paramètres de mélange. ';
var TOOLS_SYNCSEED_DISABLE = 'Désactiver le seed actuel ?';
var TOOLS_SYNCSEED_INPUTA = 'Entrez une valeur (a-zA-Z0-9) en tant que seed';
var TOOLS_BATTLE = 'Match en ligne';
var TOOLS_BATTLE_HEAD = 'Salon|Rejoindre le salon';
var TOOLS_BATTLE_TITLE = 'Rang |Statut|Temps';
var TOOLS_BATTLE_STATUS = 'Prêt|Inspection|Résolution|Résolu|Perdu';
var TOOLS_BATTLE_INFO = 'Pour affronter vos amis lors de matchs en ligne, rejoignez tous le même salon.';
var TOOLS_BATTLE_JOINALERT = 'Veuillez entrer l\'ID du salon';
var TOOLS_BATTLE_LEAVEALERT = 'Quitter le salon actuel';
var OLCOMP_UPDATELIST = 'Mettre à jour la liste des compétitions';
var OLCOMP_VIEWRESULT = 'Voir le résultat';
var OLCOMP_VIEWMYRESULT = 'Mon historique';
var OLCOMP_START = 'Démarrer !';
var OLCOMP_SUBMIT = 'Soumettre !';
var OLCOMP_SUBMITAS = 'Soumettre en tant que : ';
var OLCOMP_WCANOTICE = 'Soumettre sous votre compte WCA? (Reconnectez-vous si vous n\'êtes pas reconnu après la soumission)';
var OLCOMP_OLCOMP = 'Compétition en ligne';
var OLCOMP_ANONYM = 'Anonyme';
var OLCOMP_ME = 'Moi';
var OLCOMP_WCAACCOUNT = 'Compte WCA';
var OLCOMP_ABORT = 'Annuler la compétition et voir les résultats ?';
var OLCOMP_WITHANONYM = 'Anonymement';
var PROPERTY_IMGSIZE = 'Taille de l\'aperçu du mélange';
var PROPERTY_IMGREP = 'Montrer l\'animation du cube virtuel au clic sur l\'image du mélange';
var TIMER_INSPECT = 'Inspection';
var TIMER_SOLVE = 'Résolution';
var PROPERTY_USEMOUSE = 'Lancer le chonomètre avec la souris';
var PROPERTY_TIMEU = 'Rafraîchissement du chronomètre';
var PROPERTY_TIMEU_STR = 'Normal|0.1s|Secondes|Inspection|Aucun';
var PROPERTY_PRETIME = 'Presser la barre d\'espace pendant (en secondes)';
var PROPERTY_ENTERING = 'Entrer les temps avec';
var PROPERTY_ENTERING_STR = 'Clavier|Manuellement|Stackmat|MoYuTimer|Virtuel|Bluetooth|qCube|GanTimer|last layer training';
var PROPERTY_INTUNIT = 'Unité de saisie des nombres';
var PROPERTY_INTUNIT_STR = 'seconde|centième|milliseconde';
var PROPERTY_COLOR = 'Couleur du thème';
var PROPERTY_COLORS = 'Couleur des textes|Couleur du fond|Couleur des panneaux|Couleur des boutons|Couleur des liens|Couleur du logo|Couleur de fond du logo';
var PROPERTY_VIEW = 'Type d\'interface';
var PROPERTY_VIEW_STR = 'Auto|Mobile|Ordinateur';
var PROPERTY_UIDESIGN = 'L\'interface utilisateur est';
var PROPERTY_UIDESIGN_STR = 'Normal|Design Matériel|Normal sans ombres|Design Matériel sans ombres';
var COLOR_EXPORT = 'Sauvegarder le texte pour l\'importer ailleurs';
var COLOR_IMPORT = 'Entrer le texte exporté';
var COLOR_FAIL = 'Données incorrectes, Import échoué';
var PROPERTY_FONTCOLOR_STR = 'Noir|Blanc';
var PROPERTY_COLOR_STR = 'Personnalisée|Importer/Exporter...|Aléatoire|Style1|Style2|Style3|Noir|Blanc|Style6|solarized dark|solarized light';
var PROPERTY_FONT = 'Police du chronomètre';
var PROPERTY_FONT_STR = 'Digital aléatoire|Normal|Digital1|Digital2|Digital3|Digital4|Digital5';
var PROPERTY_FORMAT = 'Format du temps';
var PROPERTY_USEKSC = 'Utiliser les raccourcis clavier';
var PROPERTY_USEGES = 'utiliser le contrôle gestuel';
var PROPERTY_NTOOLS = 'Nombre d\'outils';
var PROPERTY_AHIDE = 'Cacher tous les éléments lors du chronométrage';
var SCRAMBLE_LAST = 'dernier';
var SCRAMBLE_NEXT = 'prochain';
var SCRAMBLE_SCRAMBLE = ' Mélange';
var SCRAMBLE_SCRAMBLING = 'Mélange';
var SCRAMBLE_LENGTH = 'Longueur';
var SCRAMBLE_INPUT = 'Entrer mélange(s)';
var SCRAMBLE_INPUTTYPE = 'Type du mélange';
var PROPERTY_VRCSPEED = 'Vitesse de base du cube virtuel (tps)';
var PROPERTY_VRCORI = 'Orientation du cube virtuel';
var PROPERTY_VRCMP = 'multi-phase';
var PROPERTY_VRCMPS = 'Aucun|CFOP|CF+OP|CFFFFOP|CFFFFOOPP|Roux';
var PROPERTY_GIIKERVRC = 'Montrer le Giiker Cube virtuel';
var PROPERTY_GIISOK_DELAY = 'Considérer le cube comme mélangé si immobile pendant';
var PROPERTY_GIISOK_DELAYS = '2s|3s|4s|5s|Jamais|Mélangé correctement';
var PROPERTY_GIISOK_KEY = 'Considérer le cube comme mélangé avec la barre d\'espace';
var PROPERTY_GIISOK_MOVE = 'Considérer le cube comme mélangé en faisant';
var PROPERTY_GIISOK_MOVES = 'U4, R4, etc|(U U\')2, (U\' U)2, etc|Jamais';
var PROPERTY_GIISBEEP = 'Bip quand le cube est considéré comme mélangé';
var PROPERTY_GIIRST = 'Remettre le Giiker Cube à zéro quand il se connecte';
var PROPERTY_GIIRSTS = 'Toujours|Sur confirmation|Jamais';
var PROPERTY_GIIMODE = 'Mode Cube Bluetooth';
var PROPERTY_GIIMODES = 'Normal|Entraînement|Entraînement continu';
var PROPERTY_VRCAH = 'Pièces inutiles pour les gros cubes';
var PROPERTY_VRCAHS = 'Masquer|Contour|Couleur|Afficher';
var CONFIRM_GIIRST = 'Remettre le Giiker Cube à zéro (état résolu)?';
var PROPERTY_GIIAED = 'Détection automatique d\'erreur hardware';
var scrdata = [
	['WCA', [
		['3x3x3', "333", 0],
		['2x2x2', "222so", 0],
		['4x4x4', "444wca", -40],
		['5x5x5', "555wca", -60],
		['6x6x6', "666wca", -80],
		['7x7x7', "777wca", -100],
		['3x3 bld', "333ni", 0],
		['3x3 fm', "333fm", 0],
		['3x3 oh', "333oh", 0],
		['clock', "clkwca", 0],
		['megaminx', "mgmp", -70],
		['pyraminx', "pyrso", -10],
		['skewb', "skbso", 0],
		['sq1', "sqrs", 0],
		['4x4 bld', "444bld", -40],
		['5x5 bld', "555bld", -60],
		['3x3 mbld', "r3ni", 5]
	]],
	['Entrer', [
		['Externe', "input", 0],
		['Compétition', "remoteComp", 0],
		['Match en ligne', "remoteBattle", 0],
		['Remote', "remoteOther", 0]
	]],
	['===WCA===', [
		['--', "blank", 0]
	]],
	['3x3x3', [
		["état aléatoire (WCA)", "333", 0],
		['mouvements aléatoires', "333o", 25],
		['3x3x3 pour débutants', "333noob", 25],
		['Arêtes seulement', "edges", 0],
		['Coins seulement', "corners", 0],
		['Assistant BLD', "nocache_333bldspec", 0],
		['Pattern Tool', "nocache_333patspec", 0],
		['3x3 ft', "333ft", 0],
		['Personnalisé', "333custom", 0]
	]],
	['3x3x3 CFOP', [
		['PLL', "pll", 0],
		['OLL', "oll", 0],
		['Dernier slot + dernier étage', "lsll2", 0],
		['Dernier étage', "ll", 0],
		['ZBLL', "zbll", 0],
		['COLL', "coll", 0],
		['CLL', "cll", 0],
		['ELL', "ell", 0],
		['2GLL', "2gll", 0],
		['ZZLL', "zzll", 0],
		['ZBLS', "zbls", 0],
		['EOLS', "eols", 0],
		['WVLS', "wvls", 0],
		['VLS', "vls", 0],
		['Croix résolue', "f2l", 0],
		['EOLine', "eoline", 0],
		['EO Cross', "eocross", 0],
		['Croix facile', "easyc", 3],
		['X-cross facile', "easyxc", 4]
	]],
	['3x3x3 Roux', [
		['2nd Block', "sbrx", 0],
		['CMLL', "cmll", 0],
		['LSE', "lse", 0],
		['LSE &lt;M, U&gt;', "lsemu", 0]
	]],
	['3x3x3 Mehta', [
		['3QB', "mt3qb", 0],
		['EOLE', "mteole", 0],
		['TDR', "mttdr", 0],
		['6CP', "mt6cp", 0],
		['CDRLL', "mtcdrll", 0],
		['L5EP', "mtl5ep", 0],
		['TTLL', "ttll", 0]
	]],
	['2x2x2', [
		["état aléatoire (WCA)", "222so", 0],
		['optimal', "222o", 0],
		['3-gen', "2223", 25],
		['EG', "222eg", 0],
		['CLL', "222eg0", 0],
		['EG1', "222eg1", 0],
		['EG2', "222eg2", 0],
		['TCLL+', "222tcp", 0],
		['TCLL-', "222tcn", 0],
		['TCLL', "222tc", 0],
		['LS', "222lsall", 0],
		['Sans barre', "222nb", 0]
	]],
	['4x4x4', [
		["WCA", "444wca", -40],
		['Au hasard', "444m", 40],
		['SiGN', "444", 40],
		['YJ', "444yj", 40],
		['Arêtes 4x4x4', "4edge", 0],
		['R,r,U,u', "RrUu", 40],
		['Last layer', "444ll", 0],
		['ELL', "444ell", 0],
		['Edge only', "444edo", 0],
		['Center only', "444cto", 0]
	]],
	['4x4x4 Yau/Hoya', [
		['UD center solved', "444ctud", 0],
		['UD+3E solved', "444ud3c", 0],
		['Last 8 dedges', "444l8e", 0],
		['RL center solved', "444ctrl", 0],
		['RLDX center solved', "444rlda", 0],
		['RLDX cross solved', "444rlca", 0]
	]],
	['5x5x5', [
		["WCA", "555wca", 60],
		['SiGN', "555", 60],
		['Arêtes 5x5x5', "5edge", 8]
	]],
	['6x6x6', [
		["WCA", "666wca", 80],
		['SiGN', "666si", 80],
		['Préfixe', "666p", 80],
		['Suffixe', "666s", 80],
		['Arêtes 6x6x6', "6edge", 8]
	]],
	['7x7x7', [
		["WCA", "777wca", 100],
		['SiGN', "777si", 100],
		['Préfixe', "777p", 100],
		['Suffixe', "777s", 100],
		['Arêtes 7x7x7', "7edge", 8]
	]],
	['Clock', [
		['WCA', "clkwca", 0],
		['wca (old)', "clkwcab", 0],
		['WCA w/o y2', "clknf", 0],
		['jaap', "clk", 0],
		['optimal', "clko", 0],
		['Concis', "clkc", 0],
		['Ordre des boutons fixe', "clke", 0]
	]],
	['Megaminx', [
		["WCA", "mgmp", 70],
		['Carrot', "mgmc", 70],
		['Ancien style', "mgmo", 70],
		['Générateur R,U', "minx2g", 30],
		['Dernier slot + dernier étage', "mlsll", 0],
		['PLL', "mgmpll", 0],
		['Last Layer', "mgmll", 0]
	]],
	['Pyraminx', [
		["état aléatoire (WCA)", "pyrso", 10],
		['optimal', "pyro", 0],
		['mouvements aléatoires', "pyrm", 25],
		['L4E', "pyrl4e", 0],
		['4 tips', "pyr4c", 0],
		['No bar', "pyrnb", 0]
	]],
	['Skewb', [
		["état aléatoire (WCA)", "skbso", 0],
		['optimal', "skbo", 0],
		['mouvements aléatoires', "skb", 25],
		['No bar', "skbnb", 0]
	]],
	['Square-1', [
		["état aléatoire (WCA)", "sqrs", 0],
		["CSP", "sqrcsp", 0],
		["PLL", "sq1pll", 0],
		['métrique face à face', "sq1h", 40],
		['torsion métrique', "sq1t", 20]
	]],
	['===AUTRE===', [
		['--', "blank", 0]
	]],
	['Taquin', [
		['état aléatoire URLD', "15prp", 0],
		['état aléatoire ^<>v', "15prap", 0],
		['état aléatoire Blank', "15prmp", 0],
		['mouvements aléatoires URLD', "15p", 80],
		['mouvements aléatoires ^<>v', "15pat", 80],
		['mouvements aléatoires Blank', "15pm", 80]
	]],
	['8 puzzle', [
		['état aléatoire URLD', "8prp", 0],
		['état aléatoire ^<>v', "8prap", 0],
		['état aléatoire Blank', "8prmp", 0]
	]],
	['LxMxN', [
		['1x3x3 (Floppy Cube)', "133", 0],
		['2x2x3 (Tower Cube)', "223", 0],
		['2x3x3 (Domino)', "233", 25],
		['3x3x4', "334", 40],
		['3x3x5', "335", 25],
		['3x3x6', "336", 40],
		['3x3x7', "337", 40],
		['8x8x8', "888", 120],
		['9x9x9', "999", 120],
		['10x10x10', "101010", 120],
		['11x11x11', "111111", 120],
		['NxNxN', "cubennn", 12]
	]],
	['Gear Cube', [
		['état aléatoire', "gearso", 0],
		['optimal', "gearo", 0],
		['mouvements aléatoires', "gear", 10]
	]],
	['Kilominx', [
		['état aléatoire', "klmso", 0],
		['Pochmann', "klmp", 30]
	]],
	['Gigaminx', [
		['Pochmann', "giga", 300]
	]],
	['Crazy Puzzle', [
		['Crazy 3x3x3', "crz3a", 30]
	]],
	['Cmetrick', [
		['Cmetrick', "cm3", 25],
		['Cmetrick Mini', "cm2", 25]
	]],
	['Helicopter Cube', [
		['Heli copter', "heli", 40],
		['Curvy copter', "helicv", 40],
		['2x2 Heli random move', "heli2x2", 70],
		['2x2 Heli by group', "heli2x2g", 5]
	]],
	['Redi Cube', [
		['état aléatoire', "rediso", 0],
		['MoYu', "redim", 8],
		['mouvements aléatoires', "redi", 20]
	]],
	['Dino Cube', [
		['état aléatoire', "dinoso", 0],
		['optimal', "dinoo", 0]
	]],
	['Ivy cube', [
		['état aléatoire', "ivyso", 0],
		['optimal', "ivyo", 0],
		['mouvements aléatoires', "ivy", 10]
	]],
	['Master Pyraminx', [
		['état aléatoire', "mpyrso", 0],
		['mouvements aléatoires', "mpyr", 42]
	]],
	['Pyraminx Crystal', [
		['Pochmann', "prcp", 70],
		['Ancien style', "prco", 70]
	]],
	['Siamese Cube', [
		['Bloc 1x1x3', "sia113", 25],
		['Block 1x2x3', "sia123", 25],
		['Bloc 2x2x2', "sia222", 25]
	]],
	['Square', [
		['Square-2', "sq2", 20],
		['Super Square-1', "ssq1t", 20]
	]],
	['Super Floppy', [
		[' ', "sfl", 25]
	]],
	['UFO', [
		['Style Jaap', "ufo", 25]
	]],
	['FTO (Face-Turning Octahedron)', [
		['état aléatoire', "ftoso", 0],
		['mouvements aléatoires', "fto", 30],
		['L3T', "ftol3t", 0],
		['L3T+LBT', "ftol4t", 0],
		['TCP', "ftotcp", 0],
		['edges only', "ftoedge", 0],
		['centers only', "ftocent", 0],
		['corners only', "ftocorn", 0],
		['Diamond état aléatoire', "dmdso", 0]
	]],
	['Icosahedron', [
		['Icosamate mouvements aléatoires', "ctico", 60]
	]],
	['=== SPÉCIAL ===', [
		['--', "blank", 0]
	]],
	['Subsets 3x3x3', [
		['Générateur R,U', "2gen", 0],
		['Générateur L,U', "2genl", 0],
		['Générateur Roux M,U', "roux", 0],
		['Générateur F,R,U', "3gen_F", 0],
		['Générateur R,U,L', "3gen_L", 0],
		['Générateur R,r,U', "RrU", 0],
		['Domino Subgroup', "333drud", 0],
		['Demi-tours seulement', "half", 0],
		['Dernier slot + dernier étage (ancien)', "lsll", 15]
	]],
	['Bandaged Cube', [
		['Bicube', "bic", 30],
		['Square-1 /,(1,0)', "bsq", 25]
	]],
	['Relais', [
		['Plusieurs 3x3x3', "r3", 5],
		['Relai 234', "r234", 0],
		['Relai 2345', "r2345", 0],
		['Relai 23456', "r23456", 0],
		['Relai 234567', "r234567", 0],
		['Relai 234 (WCA)', "r234w", 0],
		['Relai 2345 (WCA)', "r2345w", 0],
		['Relai 23456 (WCA)', "r23456w", 0],
		['Relai 234567 (WCA)', "r234567w", 0],
		['Mini Guildford', "rmngf", 0]
	]],
	['===BLAGUES===', [
		['--', "blank", 0]
	]],
	['1x1x1', [
		['x y z', "111", 25]
	]],
	['-1x-1x-1', [
		[' ', "-1", 25]
	]],
	['1x1x2', [
		[' ', "112", 25]
	]],
	['LOL', [
		[' ', "lol", 25]
	]],
	['Derrick Eide', [
		[' ', "eide", 25]
	]]
];
var SCRAMBLE_NOOBST = [
	['Tourne la face du haut', 'Tourne la face du bas'],
	['Tourne la face de droite', 'Tourne la face de gauche'],
	['Tourne la face de devant', 'Tourne la face de derrière']
];
var SCRAMBLE_NOOBSS = ' de 90 degrés dans le sens des aiguilles d\'une montre,| de 90 degrés dans le sens inverse des aiguilles d\'une montre,| de 180 degrés,';
var SCROPT_TITLE = 'Options de mélange';
var SCROPT_BTNALL = 'Complet';
var SCROPT_BTNNONE = 'Effacer';
var SCROPT_EMPTYALT = 'Veuillez sélectionner au moins un cas';
var STATS_CFM_RESET = 'Effacer tous les temps de cette session ?';
var STATS_CFM_DELSS = 'Effacer la session [%s]?';
var STATS_CFM_DELMUL = 'Nombre de valeurs effacées de l\'index en cours ?';
var STATS_CFM_DELETE = 'Effacer ce temps ?';
var STATS_COMMENT = 'Commentaire';
var STATS_REVIEW = 'Évaluer';
var STATS_DATE = 'Date';
var STATS_SSSTAT = 'Statistiques pour 1 résolution.';
var STATS_SSRETRY = 'Réessayer';
var STATS_CURROUND = 'Statistiques de la moyenne';
var STATS_CURSESSION = 'Statistiques de la session en cours';
var STATS_CURSPLIT = 'Statistiques de la phase %d de la session en cours';
var STATS_EXPORTCSV = 'Exporter en CSV';
var STATS_SSMGR_TITLE = 'Gestion des sessions';
var STATS_SSMGR_NAME = 'Nom';
var STATS_SSMGR_DETAIL = 'Détails de la session';
var STATS_SSMGR_OPS = 'Renommer|Créer|Diviser|Fusionner|Supprimer|Trier|Merge&Dedupe';
var STATS_SSMGR_ORDER = 'Classer par mélange';
var STATS_SSMGR_ODCFM = 'Trier les sessions par type de mélange ?';
var STATS_SSMGR_SORTCFM = '%d résolution(s) vont être réordonnées. Confirmer ?';
var STATS_ALERTMG = 'Déplacer tous les temps de la session [%f] à la fin de la session [%f]?';
var STATS_PROMPTSPL = 'Nombre de temps à diviser de la session [%s] (en partant de la fin)?';
var STATS_ALERTSPL = 'Il faut laisser au moins un temps, ou fusionner la session avec une autre';
var STATS_AVG = 'Moyenne';
var STATS_SUM = 'Total';
var STATS_SOLVE = 'Résolutions';
var STATS_TIME = 'temps';
var STATS_SESSION = 'Session';
var STATS_SESSION_NAME = 'Modifier le nom de la session';
var STATS_SESSION_NAMEC = 'Nom de la nouvelle session';
var STATS_STRING = 'meilleure|en cours|moins bonne|Généré par csTimer le %Y-%M-%D|résolutions/total: %d|single|moyenne sur %mk|moyenne élaguée sur %mk|Moyenne élaguée: %v{ (σ = %sgm)}|Moyenne: %v|Liste des temps:|résolutions de %s à %e|Temps passé: %d|target';
var STATS_PREC = 'Précision de la répartition des temps';
var STATS_PREC_STR = 'Auto|0.1s|0.2s|0.5s|1s|2s|5s|10s|20s|50s|100s';
var STATS_TYPELEN = 'Type de la liste %d|Longueur de la liste %d|moyenne élaguée|moyenne';
var STATS_STATCLR = 'Autoriser la suppression de tous les temps d\'une session';
var STATS_ABSIDX = 'Montrer les indices absolus dans les reports statistiques';
var STATS_XSESSION_DATE = 'toute date|dernières 24 h|7 derniers jours|30 derniers jours|365 derniers jours';
var STATS_XSESSION_NAME = 'tout nom';
var STATS_XSESSION_SCR = 'tout mélange';
var STATS_XSESSION_CALC = 'Calc';
var STATS_RSFORSS = 'Afficher les stats en cliquant sur le numéro d\'une résolution';
var PROPERTY_PRINTSCR = 'Afficher les mélanges dans les stats';
var PROPERTY_PRINTCOMM = 'Afficher les commentaires dans les statistiques';
var PROPERTY_PRINTDATE = 'Afficher la date des résolutions dans les stats';
var PROPERTY_SUMMARY = 'Montrer le résumé avant la liste de temps';
var PROPERTY_IMRENAME = 'Renommer la session immédiatement après création';
var PROPERTY_SCR2SS = 'Créer une nouvelle session lors du changement de type de mélange';
var PROPERTY_SS2SCR = 'Restaurer le type de mélange lors d\'un changement de session';
var PROPERTY_SS2PHASES = 'Restaurer le chnonométrage multi-phase lors d\'un changement de session';
var PROPERTY_STATINV = 'Inverser la liste des temps';
var PROPERTY_STATSSUM = 'Afficher le total dans la liste des temps';
var PROPERTY_STATTHRES = 'Afficher le temps cible de la meilleure session';
var PROPERTY_STATBPA = 'Montrer la meilleure moyenne possible (BPA)';
var PROPERTY_STATWPA = 'Montrer la pire moyenne possible (WPA)';
var PROPERTY_STATAL = 'Indicateurs statistiques';
var PROPERTY_STATALU = 'Indicateur de statistiques personnalisé';
var PROPERTY_HLPBS = 'Surligner les PBs';
var PROPERTY_HLPBS_STR = 'Orange foncé en WCA|En tant que couleur de lien|Bolder|Aucun';
var PROPERTY_DELMUL = 'Activer la suppression multiple';
var PROPERTY_TOOLSFUNC = 'Fonctions sélectionnées';
var PROPERTY_TRIM = 'Nombre de résolutions élaguées à chaque extrémité.';
var PROPERTY_TRIMR = 'Nombre de résolutions élaguées parmi les pires';
var PROPERTY_TRIM_MED = 'Médiane';
var PROPERTY_STKHEAD = 'Utiliser les informations d\'état du Stackmat';
var PROPERTY_TOOLPOS = 'Position du panneau Outils';
var PROPERTY_TOOLPOS_STR = 'Bas|Flottant|Haut';
var PROPERTY_HIDEFULLSOL = 'Afficher la solution progressivement';
var PROPERTY_IMPPREV = 'Importer d\'anciennes données';
var PROPERTY_AUTOEXP = 'Exportation automatique (par 100 résolutions)';
var PROPERTY_AUTOEXP_OPT = 'Jamais|Fichier|ID csTimer|Compte WCA|Compte Google|Alert Only';
var PROPERTY_SCRASIZE = 'Taille du mélange automatique';
var MODULE_NAMES = {
	"kernel": 'Global',
	"ui": 'Affichage',
	"color": 'Couleur',
	"timer": 'Chronomètre',
	"scramble": 'mélange',
	"stats": 'Statistiques',
	"tools": 'Outils',
	"vrc": 'virtuel et<br> bluetooth'
};
var BGIMAGE_URL = 'Entrez l\'adresse URL de l\'image';
var BGIMAGE_INVALID = 'Adresse invalide';
var BGIMAGE_OPACITY = 'Opacité du fond ';
var BGIMAGE_IMAGE = 'Image de fond';
var BGIMAGE_IMAGE_STR = 'Aucune|Personnalisée|CCT';
var SHOW_AVG_LABEL = 'Afficher les moyennes en cours';
var SHOW_DIFF_LABEL = 'Afficher l\'étiquette de différence';
var SHOW_DIFF_LABEL_STR = '-Vert+Rouge|-Rouge+Vert|Normal|Aucun';
var USE_LOGOHINT = 'Afficher des messages dans le bloc du logo';
var TOOLS_SCRGEN = 'Générateur de mélange';
var SCRGEN_NSCR = 'Nombre de mélanges';
var SCRGEN_PRE = 'Préfixe';
var SCRGEN_GEN = 'Générer des mélanges !';
var VRCREPLAY_TITLE = 'Replay virtuel';
var VRCREPLAY_ORI = 'ori° brute|ori° auto';
var VRCREPLAY_SHARE = 'partager le lien';
var GIIKER_CONNECT = 'Cliquer pour se connecter';
var GIIKER_RESET = 'Réinitialiser (Marquer comme résolu)';
var GIIKER_REQMACMSG = 'Please enter the MAC address of your smart hardware (xx:xx:xx:xx:xx:xx). You can find the MAC address through chrome://bluetooth-internals/#devices, or modify following options to let csTimer automatically obtain it:\nChrome: Turn on chrome://flags/#enable-experimental-web-platform-features\nBluefy: Turn on Enable BLE Advertisements';
var GIIKER_NOBLEMSG = 'Bluetooth API is not available. Ensure https access, check bluetooth is enabled on your device, and try chrome with chrome://flags/#enable-experimental-web-platform-features enabled';
var PROPERTY_SHOWAD = 'Afficher les publicités (prend effet après rechargement)';
var PROPERTY_GIIORI = 'Orientation du cube';
var LGHINT_INVALID = 'Valeur invalide !';
var LGHINT_NETERR = 'Erreur réseau !';
var LGHINT_SERVERR = 'Erreur serveur !';
var LGHINT_SUBMITED = 'Soumis';
var LGHINT_SSBEST = '%s meilleures sessions !';
var LGHINT_SCRCOPY = 'Mélange copié';
var LGHINT_LINKCOPY = 'Lien de partage copié';
var LGHINT_SOLVCOPY = 'Résolution copiée';
var LGHINT_SORT0 = 'Déjà trié';
var LGHINT_IMPORTED = 'Importer %d session(s)';
var LGHINT_IMPORT0 = 'Aucune session importée';
var LGHINT_BTCONSUC = 'Bluetooth connecté';
var LGHINT_BTDISCON = 'Bluetooth déconnecté';
var LGHINT_BTNOTSUP = 'Smart cube non pris en charge';
var LGHINT_BTINVMAC = 'Adresse mac invalide, la connexion à votre smart cube est impossible';
var LGHINT_AEXPABT = 'Export automatique interrompu';
var LGHINT_AEXPSUC = 'Export automatique réussi';
var LGHINT_AEXPFAL = 'Échec de l\'export automatique';
var EASY_SCRAMBLE_HINT = 'Change length to limit upper bound of solution length, input 2 digits to limit both lower (<= 8) and upper bound';
