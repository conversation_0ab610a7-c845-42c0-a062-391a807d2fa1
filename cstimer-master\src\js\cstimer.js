//Worker wrap for test version

importScripts(
	'lib/utillib.js',
	'lib/isaac.js',
	'lib/mathlib.js',
	'lib/grouplib.js',
	'lib/poly3dlib.js',
	'lib/pat3x3.js',
	'lib/lzstring.js',
	'lib/min2phase.js',
	'lib/cubeutil.js',
	'solver/ftocta.js',
	'solver/kilominx.js',
	'scramble/scramble.js',
	'scramble/megascramble.js',
	'scramble/scramble_333_edit.js',
	'scramble/scramble_444.js',
	'scramble/scramble_sq1_new.js',
	'scramble/pyraminx.js',
	'scramble/skewb.js',
	'scramble/2x2x2.js',
	'scramble/gearcube.js',
	'scramble/1x3x3.js',
	'scramble/2x2x3.js',
	'scramble/clock.js',
	'scramble/333lse.js',
	'scramble/mgmlsll.js',
	'scramble/kilominx.js',
	'scramble/scramble_fto.js',
	'scramble/redi.js',
	'scramble/slide.js',
	'scramble/utilscramble.js',
	'tools/tools.js',
	'tools/image.js',
	'tools/cross.js',
	'tools/eoline.js',
	'tools/roux1.js',
	'worker.js'
);
