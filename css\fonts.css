/* Font definitions */

/* System fonts for general text */
/* We'll use system fonts instead of custom fonts that don't exist */

/* <PERSON><PERSON>22 - Normal font for RTL text */
@font-face {
  font-family: "Rabar";
  font-style: normal;
  font-weight: 400;
  src: url("../assets/fonts/Rabar_22.ttf") format("truetype");
  font-display: swap;
}

/* Rabar21 - Bold font for RTL text */
@font-face {
  font-family: "Rabar";
  font-style: normal;
  font-weight: 700;
  src: url("../assets/fonts/Rabar_21.ttf") format("truetype");
  font-display: swap;
}

/* Font variables */
:root {
  --font-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  --font-timer: monospace;
  --font-rtl: "<PERSON>bar", "<PERSON>hom<PERSON>", "Arial", sans-serif; /* Added Tahoma and <PERSON><PERSON> for better Arabic support */
  --font-scale: 1; /* Default font scale for scrambles */
}
