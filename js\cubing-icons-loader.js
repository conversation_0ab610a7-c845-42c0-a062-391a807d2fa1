/**
 * Cubing Icons Loader - Handles loading cubing icons with fallback
 * Attempts to load from CDN first, falls back to local CSS if CDN fails
 */

// Function to load cubing icons from CDN with fallback
function loadCubingIcons() {
  // Create a link element to test CDN availability
  const testLink = document.createElement("link");
  testLink.rel = "stylesheet";
  testLink.href = "https://cdn.cubing.net/v0/css/@cubing/icons/css";

  // Set up error handling
  testLink.onerror = function () {
    // CDN failed, our fallback CSS is already loaded
    document.body.classList.add("cubing-icons-fallback");
  };

  testLink.onload = function () {
    // CDN loaded successfully, remove fallback class if it exists
    document.body.classList.remove("cubing-icons-fallback");
  };

  // Add to head to attempt loading
  document.head.appendChild(testLink);

  // Set a timeout as additional fallback
  setTimeout(() => {
    // Check if any cubing icon styles are actually applied
    const testElement = document.createElement("span");
    testElement.className = "cubing-icon event-333";
    testElement.style.visibility = "hidden";
    testElement.style.position = "absolute";
    document.body.appendChild(testElement);

    const computedStyle = window.getComputedStyle(testElement);
    const hasIconStyles =
      computedStyle.getPropertyValue("content") !== "none" &&
      computedStyle.getPropertyValue("content") !== "";

    if (!hasIconStyles) {
      document.body.classList.add("cubing-icons-fallback");
    }

    // Clean up test element
    document.body.removeChild(testElement);
  }, 2000);
}

// Load icons when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", loadCubingIcons);
} else {
  loadCubingIcons();
}
