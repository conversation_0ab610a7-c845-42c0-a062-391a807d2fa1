/**
 * Multi-Blind (MBLD) Manager for scTimer
 * Handles MBLD-specific functionality
 */

import { randomScrambleForEvent } from "https://cdn.cubing.net/v0/js/cubing/scramble";
import { TwistyPlayer } from "https://cdn.cubing.net/v0/js/cubing/twisty";

// MBLD state
let mbldCubeCount = 2;
let mbldScrambles = [];
let mbldAttemptInProgress = false;
let mbldCurrentScrambleIndex = 0;

// DOM Elements
let mbldCubeCountModal;
let mbldScramblesModal;
let mbldVisualizationsModal;
let mbldResultsModal;
let mbldCubeCountInput;
let mbldSolvedCountInput;
let mbldTotalCountInput;
let mbldScramblesContainer;
let mbldVisualizationsContainer;
let scrambleElement;

// Function to create MBLD modals if they don't exist
function createMBLDModals() {
  // Check if body exists
  const body = document.body;
  if (!body) {
    // Document body not found
    return;
  }

  // 1. Create Cube Count Modal if it doesn't exist
  if (!document.getElementById("mbld-cube-count-modal")) {
    const cubeCountModal = document.createElement("div");
    cubeCountModal.className = "mbld-modal";
    cubeCountModal.id = "mbld-cube-count-modal";

    cubeCountModal.innerHTML = `
      <div class="mbld-modal-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.setup">
            Multi-Blind Setup
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-cube-count-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body">
          <div class="mbld-input-group">
            <label for="mbld-cube-count" data-i18n="mbld.numberOfCubesMinimum">
              Number of cubes (minimum 2):
            </label>
            <input
              type="number"
              id="mbld-cube-count"
              min="2"
              value="2"
              class="mbld-input"
            />
          </div>
        </div>
        <div class="mbld-modal-footer">
          <button type="button" id="mbld-cube-count-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="mbld.generateScrambles">Generate Scrambles</span>
          </button>
        </div>
      </div>
    `;

    body.appendChild(cubeCountModal);
  }

  // 2. Create Scrambles Modal if it doesn't exist
  if (!document.getElementById("mbld-scrambles-modal")) {
    const scramblesModal = document.createElement("div");
    scramblesModal.className = "mbld-modal";
    scramblesModal.id = "mbld-scrambles-modal";

    scramblesModal.innerHTML = `
      <div class="mbld-modal-content mbld-scrambles-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.scrambles">
            Multi-Blind Scrambles
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-scrambles-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body" id="mbld-scrambles-container">
          <!-- Scrambles will be inserted here -->
        </div>
      </div>
    `;

    body.appendChild(scramblesModal);
  }

  // 3. Create Visualizations Modal if it doesn't exist
  if (!document.getElementById("mbld-visualizations-modal")) {
    const visualizationsModal = document.createElement("div");
    visualizationsModal.className = "mbld-modal";
    visualizationsModal.id = "mbld-visualizations-modal";

    visualizationsModal.innerHTML = `
      <div class="mbld-modal-content mbld-visualizations-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.visualizations">
            Multi-Blind Visualizations
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-visualizations-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body" id="mbld-visualizations-container">
          <!-- Visualizations will be inserted here -->
        </div>
      </div>
    `;

    body.appendChild(visualizationsModal);
  }

  // 4. Create Results Modal if it doesn't exist
  if (!document.getElementById("mbld-results-modal")) {
    const resultsModal = document.createElement("div");
    resultsModal.className = "mbld-modal";
    resultsModal.id = "mbld-results-modal";

    resultsModal.innerHTML = `
      <div class="mbld-modal-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.results">
            Multi-Blind Results
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-results-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body">
          <div id="mbld-results-info" class="mbld-info"></div>
          <div class="mbld-input-group">
            <label for="mbld-solved-count" data-i18n="mbld.numberOfCubesSolved">
              Number of cubes solved:
            </label>
            <input
              type="number"
              id="mbld-solved-count"
              min="0"
              value="0"
              class="mbld-input"
            />
          </div>
          <div class="mbld-input-group">
            <label for="mbld-total-count" data-i18n="app.outOf">
              Out of:
            </label>
            <input
              type="number"
              id="mbld-total-count"
              min="2"
              value="2"
              class="mbld-input"
              readonly
            />
          </div>
        </div>
        <div class="mbld-modal-footer">
          <button type="button" id="mbld-results-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="mbld.saveResult">Save Result</span>
          </button>
        </div>
      </div>
    `;

    body.appendChild(resultsModal);
  }

  // Apply translations if language manager is available
  if (
    window.i18nModule &&
    typeof window.i18nModule.applyTranslations === "function"
  ) {
    window.i18nModule.applyTranslations();
  }
}

// Initialize MBLD functionality
export function initMBLD() {
  // Create MBLD modals if they don't exist
  createMBLDModals();

  // Get DOM elements
  mbldCubeCountModal = document.getElementById("mbld-cube-count-modal");
  mbldScramblesModal = document.getElementById("mbld-scrambles-modal");
  mbldVisualizationsModal = document.getElementById(
    "mbld-visualizations-modal"
  );
  mbldResultsModal = document.getElementById("mbld-results-modal");
  mbldCubeCountInput = document.getElementById("mbld-cube-count");
  mbldSolvedCountInput = document.getElementById("mbld-solved-count");
  mbldTotalCountInput = document.getElementById("mbld-total-count");
  mbldScramblesContainer = document.getElementById("mbld-scrambles-container");
  mbldVisualizationsContainer = document.getElementById(
    "mbld-visualizations-container"
  );
  scrambleElement = document.getElementById("scramble");

  // Check if all required elements are available
  const requiredElements = [
    { name: "mbldCubeCountModal", element: mbldCubeCountModal },
    { name: "mbldScramblesModal", element: mbldScramblesModal },
    { name: "mbldVisualizationsModal", element: mbldVisualizationsModal },
    { name: "mbldResultsModal", element: mbldResultsModal },
    { name: "mbldCubeCountInput", element: mbldCubeCountInput },
    { name: "mbldSolvedCountInput", element: mbldSolvedCountInput },
    { name: "mbldTotalCountInput", element: mbldTotalCountInput },
    { name: "mbldScramblesContainer", element: mbldScramblesContainer },
    {
      name: "mbldVisualizationsContainer",
      element: mbldVisualizationsContainer,
    },
    { name: "scrambleElement", element: scrambleElement },
  ];

  const missingElements = requiredElements
    .filter((item) => !item.element)
    .map((item) => item.name);

  // Set up event listeners only if we have the required elements
  if (missingElements.length === 0) {
    setupEventListeners();
  } else {
    // Missing elements - skip event listener setup

    // Try to set up event listeners after a short delay
    setTimeout(() => {
      // Create MBLD modals again if they don't exist
      createMBLDModals();

      // Re-get DOM elements
      mbldCubeCountModal = document.getElementById("mbld-cube-count-modal");
      mbldScramblesModal = document.getElementById("mbld-scrambles-modal");
      mbldVisualizationsModal = document.getElementById(
        "mbld-visualizations-modal"
      );
      mbldResultsModal = document.getElementById("mbld-results-modal");
      mbldCubeCountInput = document.getElementById("mbld-cube-count");
      mbldSolvedCountInput = document.getElementById("mbld-solved-count");
      mbldTotalCountInput = document.getElementById("mbld-total-count");
      mbldScramblesContainer = document.getElementById(
        "mbld-scrambles-container"
      );
      mbldVisualizationsContainer = document.getElementById(
        "mbld-visualizations-container"
      );
      scrambleElement = document.getElementById("scramble");

      // Check if we still have missing elements
      const stillMissingElements = requiredElements
        .filter(
          (item) =>
            !document.getElementById(
              item.name.replace("mbld", "mbld-").toLowerCase()
            )
        )
        .map((item) => item.name);

      if (stillMissingElements.length > 0) {
        // Still missing MBLD elements after retry

        // Show an error message
        import("./modal-manager.js").then(({ showAlert }) => {
          // Get translations
          const translations = window.i18nModule?.translations || {};
          const mbldTranslations = translations.mbld || {};
          const modalTranslations = translations.modals || {};

          showAlert(
            mbldTranslations.initError ||
              "Error initializing Multi-Blind. Please refresh the page and try again.",
            modalTranslations.error || "Error"
          );
        });
      } else {
        // Set up event listeners
        setupEventListeners();
      }
    }, 500);
  }
}

// Set up event listeners for MBLD modals
function setupEventListeners() {
  // Cube count modal
  const cubeCountCloseBtn = document.getElementById("mbld-cube-count-close");
  if (cubeCountCloseBtn) {
    cubeCountCloseBtn.addEventListener("click", () => {
      if (mbldCubeCountModal) {
        mbldCubeCountModal.classList.remove("show");
      }
    });
  }

  const cubeCountSaveBtn = document.getElementById("mbld-cube-count-save");
  if (cubeCountSaveBtn) {
    cubeCountSaveBtn.addEventListener("click", () => {
      if (mbldCubeCountInput) {
        const count = parseInt(mbldCubeCountInput.value);
        if (count >= 2) {
          mbldCubeCount = count;
          if (mbldCubeCountModal) {
            mbldCubeCountModal.classList.remove("show");
          }
          generateMBLDScrambles(count);
        } else {
          alert("Please enter at least 2 cubes.");
        }
      }
    });
  }

  // Scrambles modal
  const scramblesCloseBtn = document.getElementById("mbld-scrambles-close");
  if (scramblesCloseBtn) {
    scramblesCloseBtn.addEventListener("click", () => {
      if (mbldScramblesModal) {
        mbldScramblesModal.classList.remove("show");
      }
    });
  }

  // Visualizations modal
  const visualizationsCloseBtn = document.getElementById(
    "mbld-visualizations-close"
  );
  if (visualizationsCloseBtn) {
    visualizationsCloseBtn.addEventListener("click", () => {
      if (mbldVisualizationsModal) {
        mbldVisualizationsModal.classList.remove("show");
      }
    });
  }

  // Results modal
  const resultsCloseBtn = document.getElementById("mbld-results-close");
  if (resultsCloseBtn) {
    resultsCloseBtn.addEventListener("click", () => {
      // Get translations
      const translations = window.i18nModule?.translations || {};
      const mbldTranslations = translations.mbld || {};
      const modalTranslations = translations.modals || {};

      // Don't allow closing without saving
      import("./modal-manager.js").then(({ showAlert }) => {
        showAlert(
          mbldTranslations.saveFirst || "Please save your result first.",
          modalTranslations.warning || "Warning"
        );
      });
    });
  }

  const resultsSaveBtn = document.getElementById("mbld-results-save");
  if (resultsSaveBtn) {
    resultsSaveBtn.addEventListener("click", saveMBLDResult);
  }

  // Make scramble area clickable for MBLD
  if (scrambleElement) {
    // Get translations
    const translations = window.i18nModule?.translations || {};
    const mbldTranslations = translations.mbld || {};

    // Remove any existing click event listeners to avoid duplicates
    const newScrambleElement = scrambleElement.cloneNode(true);
    if (scrambleElement.parentNode) {
      scrambleElement.parentNode.replaceChild(
        newScrambleElement,
        scrambleElement
      );
      scrambleElement = newScrambleElement;
    }

    // Add the click event listener
    scrambleElement.addEventListener("click", () => {
      if (scrambleElement.classList.contains("mbld-clickable")) {
        // No need to set tooltip as it's now part of the text content

        // Show the scrambles modal (not visualizations)
        showMBLDScrambles();
      }
    });
  }
}

// Show the cube count modal
export function showCubeCountModal() {
  // Always set a default of at least 2 cubes
  mbldCubeCount = Math.max(2, mbldCubeCount);

  // Create MBLD modals if they don't exist
  createMBLDModals();

  // Get the elements again
  mbldCubeCountModal = document.getElementById("mbld-cube-count-modal");
  mbldCubeCountInput = document.getElementById("mbld-cube-count");

  // Check if the input element exists
  if (!mbldCubeCountInput) {
    // MBLD cube count input is still null after creating modals

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const mbldTranslations = translations.mbld || {};
    const modalTranslations = translations.modals || {};

    // Show an error message
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        mbldTranslations.initError ||
          "Error initializing Multi-Blind. Please refresh the page and try again.",
        modalTranslations.error || "Error"
      );
    });

    return;
  }

  // Set the value if the input exists
  mbldCubeCountInput.value = mbldCubeCount;

  // Show the modal if it exists
  if (mbldCubeCountModal) {
    mbldCubeCountModal.classList.add("show");
  } else {
    // MBLD cube count modal is still null after creating modals

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const mbldTranslations = translations.mbld || {};
    const modalTranslations = translations.modals || {};

    // Show an error message
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        mbldTranslations.modalError ||
          "Error showing Multi-Blind setup. Please refresh the page and try again.",
        modalTranslations.error || "Error"
      );
    });
  }
}

// Generate MBLD scrambles
export async function generateMBLDScrambles(count) {
  // Show loading state
  const scrambleLoader = document.getElementById("scramble-loader");
  const scrambleText = document.getElementById("scramble-text");

  if (scrambleLoader) scrambleLoader.style.display = "inline-block";
  if (scrambleText) {
    scrambleText.style.display = "none";
    // Make sure the text is not hidden by removing the hidden class
    scrambleText.classList.remove("hidden");
  }

  // Clear previous scrambles
  mbldScrambles = [];

  try {
    // Generate scrambles in parallel with timeout
    const scramblePromises = [];
    for (let i = 0; i < count; i++) {
      scramblePromises.push(
        Promise.race([
          randomScrambleForEvent("333bf"),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Scramble generation timed out")),
              5000
            )
          ),
        ])
      );
    }

    const results = await Promise.allSettled(scramblePromises);

    // Process the results, using simple scrambles for any that failed
    mbldScrambles = results.map((result, index) => {
      if (result.status === "fulfilled" && result.value) {
        return result.value.toString();
      } else {
        // Failed to generate MBLD scramble, using simple scramble
        return generateSimpleMBLDScramble();
      }
    });
  } catch (error) {
    // Error generating MBLD scrambles

    // Generate simple scrambles as fallback
    mbldScrambles = [];
    for (let i = 0; i < count; i++) {
      mbldScrambles.push(generateSimpleMBLDScramble());
    }
  }

  // Update the display
  updateMBLDScrambleDisplay();

  // Make scramble area clickable
  if (scrambleElement) {
    // Get translations
    const translations = window.i18nModule?.translations || {};
    const mbldTranslations = translations.mbld || {};

    // No need to set tooltip as it's now part of the text content

    scrambleElement.classList.add("mbld-clickable");
  }

  // Update the current scramble index
  mbldCurrentScrambleIndex = 0;

  // Set attempt in progress
  mbldAttemptInProgress = true;

  // Force update the visualization in the main timer
  // This will ensure the clickable visualization box appears immediately
  if (typeof updateVisualization === "function") {
    try {
      updateVisualization();
    } catch (error) {
      // Silent error handling
    }
  }
}

// Generate a simple non-WCA scramble as fallback
function generateSimpleMBLDScramble() {
  const moves = ["R", "L", "U", "D", "F", "B"];
  const modifiers = ["", "'", "2"];
  let scramble = [];

  let lastMove = null;
  const length = 20;

  for (let i = 0; i < length; i++) {
    let move;
    do {
      move = moves[Math.floor(Math.random() * moves.length)];
    } while (move === lastMove);

    lastMove = move;
    const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
    scramble.push(move + modifier);
  }

  return scramble.join(" ");
}

// Update the MBLD scramble display
function updateMBLDScrambleDisplay() {
  const scrambleLoader = document.getElementById("scramble-loader");
  const scrambleText = document.getElementById("scramble-text");

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const mbldTranslations = translations.mbld || {};

  if (scrambleLoader) scrambleLoader.style.display = "none";
  if (scrambleText) {
    scrambleText.style.display = "inline";
    // Change from "X Cubes" to "Click to view all X scrambles"
    scrambleText.textContent = (
      mbldTranslations.clickToViewScramblesCount ||
      "Click to view all {0} scrambles"
    ).replace("{0}", mbldScrambles.length);

    // Make sure the text is not hidden
    scrambleText.classList.remove("hidden");
  }

  // Remove the tooltip as it's now part of the text content
  if (scrambleElement) {
    scrambleElement.removeAttribute("title");
  }

  // Update the scrambles container
  updateScramblesContainer();

  // Update MBLD-specific stats display
  updateMBLDStatsDisplay();
}

// Update MBLD-specific stats display
function updateMBLDStatsDisplay() {
  // Get the stats container
  const statsContainer = document.querySelector(".stats-container");
  if (!statsContainer) return;

  // Get MBLD times from the global timesMap
  let times = [];

  // First try to get times from the global timesMap
  if (window.timesMap && window.timesMap["333mbf"]) {
    times = window.timesMap["333mbf"];
  } else {
    // Fallback to localStorage if timesMap is not available
    const currentEvent = "333mbf";
    const timesKey = `scTimer-times`;
    const timesJson = localStorage.getItem(timesKey) || "{}";

    try {
      const allTimes = JSON.parse(timesJson);
      times = allTimes[currentEvent] || [];
    } catch (error) {
      // Error parsing MBLD times
      times = [];
    }
  }

  // Extract points from times
  const points = times
    .map((time) => {
      // For MBLD, we can use the score property directly
      if (time.isMBLD && typeof time.score === "number") {
        return time.score;
      } else if (time.mbldResult && typeof time.mbldResult.score === "number") {
        // For backward compatibility with old format
        return time.mbldResult.score;
      }
      return null;
    })
    .filter((score) => score !== null);

  // Calculate statistics
  const totalAttempts = times.length;
  const bestPoints = points.length > 0 ? Math.max(...points) : "-";

  // Calculate success rate based on total cubes solved vs. total cubes attempted
  let totalCubesAttempted = 0;
  let totalCubesSolved = 0;

  times.forEach((time) => {
    if (time.isMBLD) {
      // New format
      totalCubesAttempted += time.total || 0;
      totalCubesSolved += time.solved || 0;
    } else if (time.mbldResult) {
      // Old format
      totalCubesAttempted += time.mbldResult.total || 0;
      totalCubesSolved += time.mbldResult.solved || 0;
    }
  });

  const successRate =
    totalCubesAttempted > 0
      ? ((totalCubesSolved / totalCubesAttempted) * 100).toFixed(1)
      : "-";

  // Calculate averages
  const ao5 = calculateMBLDAverage(points, 5);
  const ao12 = calculateMBLDAverage(points, 12);
  const mean = calculateMBLDMean(points);

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const mbldTranslations = translations.mbld || {};
  const statsTranslations = translations.stats || {};

  // Create MBLD-specific stats HTML
  const statsHTML = `
    <div class="stats-content mbld-stats-content">
      <div class="stats-row">
        <div class="stats-label">${
          statsTranslations.attempts || "Attempts"
        }:</div>
        <div class="stats-value">${totalAttempts}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${
          mbldTranslations.totalCubes || "Total Cubes"
        }:</div>
        <div class="stats-value">${totalCubesAttempted}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${
          mbldTranslations.cubesSolved || "Cubes Solved"
        }:</div>
        <div class="stats-value">${totalCubesSolved}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${
          mbldTranslations.bestPoints || "Best Points"
        }:</div>
        <div class="stats-value">${bestPoints}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${
          mbldTranslations.successRate || "Success Rate"
        }:</div>
        <div class="stats-value">${successRate}%</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.mean || "Mean"}:</div>
        <div class="stats-value">${mean} ${
    mbldTranslations.points || "pts"
  }</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.ao5 || "ao5"}:</div>
        <div class="stats-value">${ao5} ${
    mbldTranslations.points || "pts"
  }</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.ao12 || "ao12"}:</div>
        <div class="stats-value">${ao12} ${
    mbldTranslations.points || "pts"
  }</div>
      </div>
    </div>
  `;

  // Update stats container
  statsContainer.innerHTML = statsHTML;
}

// Helper function to calculate MBLD average of N
function calculateMBLDAverage(values, n) {
  if (values.length < n) return "-";

  // Get the most recent n values
  const recentValues = values.slice(-n);

  // For MBLD, we don't remove best and worst
  // But we need to handle DNF results (which would be null or undefined)
  const validValues = recentValues.filter(
    (val) => val !== null && val !== undefined
  );

  // If there are no valid values, return DNF
  if (validValues.length === 0) return "DNF";

  // If there are fewer valid values than required, it's a DNF
  if (validValues.length < n) return "DNF";

  const sum = validValues.reduce((acc, val) => acc + val, 0);
  const avg = sum / validValues.length;

  return avg.toFixed(2);
}

// Helper function to calculate MBLD mean
function calculateMBLDMean(values) {
  if (values.length === 0) return "-";

  // Filter out null or undefined values (DNFs)
  const validValues = values.filter((val) => val !== null && val !== undefined);

  // If there are no valid values, return DNF
  if (validValues.length === 0) return "DNF";

  const sum = validValues.reduce((acc, val) => acc + val, 0);
  const mean = sum / validValues.length;

  return mean.toFixed(2);
}

// Update the scrambles container with all scrambles
function updateScramblesContainer() {
  if (!mbldScramblesContainer) return;

  // Clear previous content
  mbldScramblesContainer.innerHTML = "";

  // Add each scramble
  mbldScrambles.forEach((scramble, index) => {
    const scrambleItem = document.createElement("div");
    scrambleItem.className = "mbld-scramble-item";

    const scrambleHeader = document.createElement("div");
    scrambleHeader.className = "mbld-scramble-header";

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const mbldTranslations = translations.mbld || {};

    const scrambleNumber = document.createElement("div");
    scrambleNumber.className = "mbld-scramble-number";
    scrambleNumber.textContent = `${mbldTranslations.cubeNumber || "Cube"} ${
      index + 1
    }`;

    scrambleHeader.appendChild(scrambleNumber);
    scrambleItem.appendChild(scrambleHeader);

    const scrambleText = document.createElement("div");
    scrambleText.className = "mbld-scramble-text";
    scrambleText.textContent = scramble;
    scrambleItem.appendChild(scrambleText);

    mbldScramblesContainer.appendChild(scrambleItem);
  });
}

// Create a visualization for a scramble
function createVisualization(scramble, containerId) {
  try {
    const container = document.getElementById(containerId);
    if (!container) {
      return;
    }

    // Create a new twisty player
    const twistyPlayer = new TwistyPlayer({
      puzzle: "3x3x3",
      alg: scramble,
      background: "none",
      controlPanel: "none",
      visualization: "2D",
    });

    // Set the size
    twistyPlayer.style.width = "100%";
    twistyPlayer.style.height = "100%";

    container.appendChild(twistyPlayer);
  } catch (error) {
    // Error creating MBLD visualization

    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML =
        '<div style="color: var(--text-color); text-align: center; padding: 10px;">' +
        '<i class="fas fa-cube" style="font-size: 24px; margin-bottom: 5px;"></i><br>' +
        "Visualization unavailable</div>";
    }
  }
}

// Show all MBLD scrambles
export function showMBLDScrambles() {
  if (mbldScrambles.length === 0) return;

  // Update the scrambles container
  updateScramblesContainer();

  // Show the modal
  mbldScramblesModal.classList.add("show");
}

// Function to hide the visualizations modal
function hideVisualizationsModal() {
  if (mbldVisualizationsModal) {
    mbldVisualizationsModal.classList.remove("show");
  }
}

// Show all MBLD visualizations
export function showMBLDVisualizations() {
  // Get translations
  const translations = window.i18nModule?.translations || {};
  const mbldTranslations = translations.mbld || {};
  const modalTranslations = translations.modals || {};

  if (mbldScrambles.length === 0) {
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        mbldTranslations.noScrambles ||
          "No MBLD scrambles available. Please select the 3x3 Multi-Blind event first.",
        modalTranslations.warning || "Warning"
      );
    });
    return;
  }

  // Make sure the modal elements exist
  if (!mbldVisualizationsModal) {
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        mbldTranslations.visualizationNotFound ||
          "Visualization modal not found. Please refresh the page and try again.",
        modalTranslations.error || "Error"
      );
    });
    return;
  }

  if (!mbldVisualizationsContainer) {
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        mbldTranslations.containerNotFound ||
          "Visualization container not found. Please refresh the page and try again.",
        modalTranslations.error || "Error"
      );
    });
    return;
  }

  // Update the visualizations container
  updateVisualizationsContainer();

  // Show the modal
  mbldVisualizationsModal.classList.add("show");

  // Add a direct click event to close the modal when clicking outside the content
  const closeOnOutsideClick = function (e) {
    if (e.target === mbldVisualizationsModal) {
      hideVisualizationsModal();
      mbldVisualizationsModal.removeEventListener("click", closeOnOutsideClick);
    }
  };

  mbldVisualizationsModal.addEventListener("click", closeOnOutsideClick);
}

// Update the visualizations container with all visualizations
function updateVisualizationsContainer() {
  if (!mbldVisualizationsContainer) {
    return;
  }

  // Clear previous content
  mbldVisualizationsContainer.innerHTML = "";

  // Create a wrapper for all visualizations
  const visualizationsWrapper = document.createElement("div");
  visualizationsWrapper.className = "mbld-visualizations-wrapper";

  // Add a title
  const title = document.createElement("h3");
  title.className = "mbld-visualizations-title";

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const mbldTranslations = translations.mbld || {};

  // Format the title with the cube count
  const titleText =
    mbldTranslations.visualizationsTitle ||
    "Multi-Blind Visualizations ({0} cubes)";
  title.textContent = titleText.replace("{0}", mbldScrambles.length);

  visualizationsWrapper.appendChild(title);

  // Add each visualization
  mbldScrambles.forEach((scramble, index) => {
    const visualizationItem = document.createElement("div");
    visualizationItem.className = "mbld-visualization-item";

    const visualizationHeader = document.createElement("div");
    visualizationHeader.className = "mbld-visualization-header";

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const mbldTranslations = translations.mbld || {};

    const visualizationNumber = document.createElement("div");
    visualizationNumber.className = "mbld-visualization-number";
    visualizationNumber.textContent = `${
      mbldTranslations.cubeNumber || "Cube"
    } ${index + 1}`;

    visualizationHeader.appendChild(visualizationNumber);
    visualizationItem.appendChild(visualizationHeader);

    // Create a two-column layout
    const twoColumnContainer = document.createElement("div");
    twoColumnContainer.className = "mbld-two-column-container";

    // Left column for scramble
    const scrambleColumn = document.createElement("div");
    scrambleColumn.className = "mbld-scramble-column";

    // Add scramble text
    const scrambleText = document.createElement("div");
    scrambleText.className = "mbld-visualization-scramble";
    scrambleText.textContent = scramble;
    scrambleColumn.appendChild(scrambleText);

    // Right column for visualization
    const visualizationColumn = document.createElement("div");
    visualizationColumn.className = "mbld-visualization-column";

    const visualizationContainer = document.createElement("div");
    visualizationContainer.className = "mbld-cube-visualization";
    visualizationContainer.id = `mbld-viz-${index}`;
    visualizationColumn.appendChild(visualizationContainer);

    // Add columns to the container
    twoColumnContainer.appendChild(scrambleColumn);
    twoColumnContainer.appendChild(visualizationColumn);

    // Add the two-column container to the item
    visualizationItem.appendChild(twoColumnContainer);

    visualizationsWrapper.appendChild(visualizationItem);
  });

  // Add the wrapper to the container
  mbldVisualizationsContainer.appendChild(visualizationsWrapper);

  // Now create all visualizations after the DOM is updated
  setTimeout(() => {
    mbldScrambles.forEach((scramble, index) => {
      createVisualization(scramble, `mbld-viz-${index}`);
    });
  }, 100);
}

// Show the MBLD results modal
export function showMBLDResultsModal() {
  if (!mbldAttemptInProgress) return;

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const mbldTranslations = translations.mbld || {};

  // Set the total count
  mbldSolvedCountInput.value = 0;
  mbldSolvedCountInput.max = mbldCubeCount;
  mbldTotalCountInput.value = mbldCubeCount;

  // Calculate time limit: 10 minutes per cube, capped at 60 minutes
  const timeLimit = Math.min(mbldCubeCount * 10, 60);

  // Add time limit information to the modal
  const infoElement = document.getElementById("mbld-results-info");
  if (infoElement) {
    const timeLimitText =
      mbldTranslations.timeLimit || "Time limit: {0} minutes";
    infoElement.textContent = timeLimitText.replace("{0}", timeLimit);
    infoElement.classList.remove("warning");
  }

  // Show the modal
  mbldResultsModal.classList.add("show");
}

// Save the MBLD result
function saveMBLDResult() {
  const solved = parseInt(mbldSolvedCountInput.value);
  const total = parseInt(mbldTotalCountInput.value);

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const mbldTranslations = translations.mbld || {};
  const modalTranslations = translations.modals || {};

  if (isNaN(solved) || solved < 0 || solved > total) {
    import("./modal-manager.js").then(({ showAlert }) => {
      showAlert(
        mbldTranslations.enterValidNumber ||
          "Please enter a valid number of solved cubes.",
        modalTranslations.error || "Error"
      );
    });
    return;
  }

  // Calculate the MBLD score
  const score = calculateMBLDScore(solved, total);

  // Get the time from the timer
  const timerElement = document.getElementById("timer");
  const timeInMs = timerElement
    ? parseFloat(timerElement.textContent) * 1000
    : 0;
  const timeInMinutes = timeInMs / 60000;

  // Calculate time limit: 10 minutes per cube, capped at 60 minutes
  const timeLimit = Math.min(total * 10, 60);

  // Check if time limit exceeded
  let isDNF = false;
  const infoElement = document.getElementById("mbld-results-info");

  if (timeInMinutes > timeLimit) {
    // Time limit exceeded, result is DNF
    isDNF = true;
    if (infoElement) {
      infoElement.textContent =
        mbldTranslations.timeLimitExceeded ||
        "Time limit exceeded. Result will be DNF.";
      infoElement.classList.add("warning");
    }
  } else if (score < 0) {
    // Negative points, result is DNF
    isDNF = true;
    if (infoElement) {
      infoElement.textContent =
        mbldTranslations.negativePoints ||
        "Negative points. Result will be DNF.";
      infoElement.classList.add("warning");
    }
  }

  // Create a time object with all MBLD data directly
  const result = {
    time: isDNF ? "DNF" : timeInMs,
    date: new Date().toISOString(),
    scramble: mbldScrambles.length > 0 ? mbldScrambles[0] : "",
    scrambles: [...mbldScrambles], // Store all scrambles
    penalty: null,
    solved: solved,
    total: total,
    score: score,
    result: isDNF ? "DNF" : formatMBLDResult(score, timeInMs),
    isDNF: isDNF,
    isMBLD: true, // Mark as MBLD result
  };

  // Dispatch a custom event with the result
  const event = new CustomEvent("mbldResultSaved", { detail: result });
  document.dispatchEvent(event);

  // Update MBLD-specific stats display
  updateMBLDStatsDisplay();

  // Reset the MBLD state
  mbldAttemptInProgress = false;

  // Hide the modal
  mbldResultsModal.classList.remove("show");
}

// Calculate the MBLD score
function calculateMBLDScore(solved, total) {
  return solved - (total - solved);
}

// Format the MBLD result for display
function formatMBLDResult(score, timeInMs) {
  const timeInMinutes = Math.floor(timeInMs / 60000);
  const timeInSeconds = ((timeInMs % 60000) / 1000).toFixed(2);
  return `${score} (${timeInMinutes}:${timeInSeconds.padStart(5, "0")})`;
}

// Format time in milliseconds to a human-readable string
// This function is used in the MBLD result modal
export function formatTime(timeInMs) {
  const timeInMinutes = Math.floor(timeInMs / 60000);
  const timeInSeconds = Math.floor((timeInMs % 60000) / 1000);
  const timeInMilliseconds = timeInMs % 1000;

  return `${timeInMinutes}:${timeInSeconds
    .toString()
    .padStart(2, "0")}.${timeInMilliseconds.toString().padStart(3, "0")}`;
}

// Check if MBLD is in progress
export function isMBLDInProgress() {
  return mbldAttemptInProgress;
}

// Reset MBLD state
export function resetMBLD() {
  mbldAttemptInProgress = false;
  mbldScrambles = [];
  mbldCurrentScrambleIndex = 0;

  // Remove clickable class from scramble area
  if (scrambleElement) {
    scrambleElement.classList.remove("mbld-clickable");
  }

  // Hide all MBLD modals
  const mbldModals = [
    mbldCubeCountModal,
    mbldScramblesModal,
    mbldVisualizationsModal,
    mbldResultsModal,
  ];

  mbldModals.forEach((modal) => {
    if (modal && modal.classList.contains("show")) {
      modal.classList.remove("show");
    }
  });

  // Clear any MBLD containers
  const mbldContainers = [mbldScramblesContainer, mbldVisualizationsContainer];

  mbldContainers.forEach((container) => {
    if (container) {
      container.innerHTML = "";
    }
  });
}

// Get the current MBLD cube count
export function getMBLDCubeCount() {
  return mbldCubeCount;
}

// Get the current MBLD scrambles
export function getMBLDScrambles() {
  return [...mbldScrambles];
}
