import { Packet } from "./packet/packet";
import { SignalDecoder } from "./signal-decoder";
import { stackmatWorkletProcessor } from "./audio-worklet-processor";

export class AudioProcessor {
  private readonly callback: (packet: Packet) => void;
  private stream?: MediaStream;
  private context?: AudioContext;
  private source?: MediaStreamAudioSourceNode;
  private workletNode?: AudioWorkletNode;
  private scriptProcessor?: ScriptProcessorNode;

  constructor(callback: (packet: Packet) => void) {
    this.callback = callback;
  }

  public start(deviceId?: string) {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      // Audio constraints matching csTimer's approach for better Android compatibility
      const audioConstraints: MediaTrackConstraints = {
        echoCancellation: false,
        noiseSuppression: false,
        // Note: Don't set autoGainControl to false - let csTimer-style AGC handle it
        // Don't set latency or channelCount - keep it simple like csTimer
      };

      // Add device selection if specified
      if (deviceId) {
        audioConstraints.deviceId = { exact: deviceId };
        console.log("Stackmat using specific device:", deviceId);
      } else {
        console.log("Stackmat using default device");
      }

      navigator.mediaDevices
        .getUserMedia({
          audio: audioConstraints,
        })
        .then((stream: MediaStream) => {
          this.stream = stream;

          // Create AudioContext simply like csTimer
          this.context = new AudioContext();

          // Resume context if suspended (common on mobile)
          if (this.context.state === "suspended") {
            this.context.resume();
          }

          this.source = this.context.createMediaStreamSource(this.stream);
          const signalDecoder = new SignalDecoder(
            this.context.sampleRate,
            this.callback
          );

          // Use ScriptProcessorNode by default like csTimer for better Android compatibility
          console.log(
            "Using ScriptProcessorNode (csTimer approach) for better Android compatibility"
          );
          this.setupScriptProcessorFallback(signalDecoder);
        })
        .catch((error: Error) => {
          console.error("getUserMedia error:", error);
          // Re-throw with more context for better error handling
          throw error;
        });
    }
  }

  private setupScriptProcessorFallback(signalDecoder: SignalDecoder) {
    if (!this.context || !this.source) return;

    try {
      // Use csTimer's buffer size and AGC approach for better Y splitter compatibility
      const bufferSize = 1024; // Same as csTimer
      this.scriptProcessor = this.context.createScriptProcessor(
        bufferSize,
        1,
        1
      );

      // Implement csTimer-style AGC for better Y splitter compatibility
      let lastPower = 1;
      const agcFactor = 0.001 / (this.context.sampleRate / 1200); // csTimer's AGC factor

      this.scriptProcessor.onaudioprocess = (event: AudioProcessingEvent) => {
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);

        // Apply csTimer-style AGC processing
        const processedData = new Float32Array(inputData.length);
        for (let i = 0; i < inputData.length; i++) {
          const power = inputData[i] * inputData[i];
          lastPower = Math.max(
            0.0001,
            lastPower + (power - lastPower) * agcFactor
          );
          const gain = 1 / Math.sqrt(lastPower);
          processedData[i] = inputData[i] * gain;
        }

        signalDecoder.decode(processedData);
      };

      // Connect the audio processing chain
      this.source.connect(this.scriptProcessor);
      this.scriptProcessor.connect(this.context.destination);

      console.log(
        "ScriptProcessorNode fallback with csTimer-style AGC initialized"
      );
    } catch (fallbackError) {
      console.error("ScriptProcessorNode fallback also failed:", fallbackError);
    }
  }

  public stop() {
    // Clean up AudioWorkletNode if it exists
    if (this.context && this.source && this.workletNode) {
      this.source.disconnect(this.workletNode);
      this.workletNode.disconnect(this.context.destination);
      this.workletNode = undefined;
    }

    // Clean up ScriptProcessorNode if it exists
    if (this.context && this.source && this.scriptProcessor) {
      this.source.disconnect(this.scriptProcessor);
      this.scriptProcessor.disconnect(this.context.destination);
      this.scriptProcessor = undefined;
    }

    // Clean up source and context
    if (this.source) {
      this.source = undefined;
    }

    if (this.context) {
      this.context.close().finally(() => {
        this.context = undefined;
      });
    }

    // Clean up media stream
    if (this.stream) {
      this.stream
        .getTracks()
        .forEach((track: MediaStreamTrack) => track.stop());
      this.stream = undefined;
    }
  }
}
