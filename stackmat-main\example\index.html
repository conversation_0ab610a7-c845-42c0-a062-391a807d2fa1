<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Stackmat Demo</title>
</head>
<body>
    <p id="status">Not Connected</p>
    <p>Last Packet Status: <span id="timerstatus"></span></p>
    <p>Last Packet Milliseconds: <span id="timermillis">0</span></p>
    <p>Last Packet Time: <span id="timer">00:00:00</span></p>
    <button id="toggle" onclick="toggle()">Start</button>
    <script src="../dist/umd/stackmat.js"></script>
    <script>
        let capturing = false
        const stackmat = new Stackmat()

        stackmat.on('packetReceived', function(packet) {
            document.getElementById('timerstatus').innerText = packet.status
            document.getElementById('timermillis').innerText = packet.timeInMilliseconds
            document.getElementById('timer').innerText = packet.timeAsString
        })

        stackmat.on('timerConnected', function() {
            document.getElementById('status').innerText = 'Connected'
            console.log('Timer Connected')
        })

        stackmat.on('timerDisconnected', function() {
            document.getElementById('status').innerText = 'Not Connected'
            document.getElementById('timerstatus').innerText = ''
            console.log('Timer Disconnected')
        })

        stackmat.on('leftHandDown', function() {
            console.log('Left Hand Down')
        })

        stackmat.on('leftHandUp', function() {
            console.log('Left Hand Up')
        })

        stackmat.on('rightHandDown', function() {
            console.log('Right Hand Down')
        })

        stackmat.on('rightHandUp', function() {
            console.log('Right Hand Up')
        })

        stackmat.on('ready', function() {
            console.log('Ready')
        })

        stackmat.on('unready', function () {
            console.error('Unready')
        })

        stackmat.on('starting', function () {
            console.log('Starting')
        })

        stackmat.on('started', function() {
            console.log('Started')
        })

        stackmat.on('stopped', function(packet) {
            console.log('Stopped: ' + packet.timeAsString)
        })

        stackmat.on('reset', function() {
            console.log('Reset')
        })

        function toggle() {
            capturing = !capturing

            if (capturing) {
                document.getElementById('toggle').innerText = 'Stop'
                stackmat.start()
            } else {
                document.getElementById('toggle').innerText = 'Start'
                stackmat.stop()
            }
        }
    </script>
</body>
</html>
