<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <title>scTimer</title>
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon" />
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#a57865" />
    <meta
      name="description"
      content="A speedcubing timer with WCA inspection and statistics"
    />
    <link rel="manifest" href="manifest.json" />
    <!-- Mobile app support -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="scTimer" />
    <link rel="apple-touch-icon" href="assets/icons/152x152.png" />
    <!-- iOS PWA specific tags -->
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />

    <!-- iOS splash screens -->
    <!-- iPhone X (1125px x 2436px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)"
      href="assets/icons/512x512.png"
    />
    <!-- iPhone 8, 7, 6s, 6 (750px x 1334px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (1242px x 2208px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)"
      href="assets/icons/512x512.png"
    />
    <!-- iPhone 5 (640px x 1136px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Pro 12.9" (2048px x 2732px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Pro 11" (1668px x 2388px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Pro 10.5" (1668px x 2224px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Mini, Air (1536px x 2048px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- Cubing Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.cubing.net/v0/css/@cubing/icons"
      crossorigin="anonymous"
    />
    <!-- Font Awesome for UI icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/fonts.css" />
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" href="css/fmc-manager.css" />
  </head>
  <body>
    <!-- Times panel (slide-in from left) -->
    <div class="times-panel" id="times-panel">
      <div class="times-panel-header">
        <div class="times-panel-title" data-i18n="times.title">Solve Times</div>
        <button
          type="button"
          class="times-panel-close mobile-only"
          id="times-panel-close"
          title="Close"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="times-list">
        <ul id="times-list"></ul>
      </div>
      <div class="times-panel-footer">
        <div class="footer-buttons">
          <button type="button" id="clear-times-btn">
            <i class="fas fa-trash"></i>
            <span class="clear-times-text" data-i18n="times.clear"
              >Clear Times</span
            >
          </button>
          <button type="button" id="edit-session-btn" class="hidden">
            <i class="fas fa-edit"></i>
            <span class="edit-session-text" data-i18n="sessions.editSession"
              >Edit Session</span
            >
          </button>
        </div>
        <div class="footer-actions">
          <!-- Space for additional buttons if needed -->
        </div>
      </div>
    </div>

    <div class="container">
      <!-- Header with controls -->
      <header>
        <div class="header-left">
          <!-- Empty space for balance -->
        </div>
        <div class="header-center">
          <div class="event-selector">
            <button
              type="button"
              class="event-selector-btn"
              id="event-selector-btn"
            >
              <span
                class="cubing-icon event-333"
                id="current-event-icon"
              ></span>
              <span id="current-event-text" data-i18n="events.333">3x3x3</span>
            </button>
            <div class="event-dropdown" id="event-dropdown">
              <div class="event-option" data-event="333">
                <span class="cubing-icon event-333"></span>
                <span data-i18n="events.333">3x3x3</span>
              </div>
              <div class="event-option" data-event="222">
                <span class="cubing-icon event-222"></span>
                <span data-i18n="events.222">2x2x2</span>
              </div>
              <div class="event-option" data-event="444">
                <span class="cubing-icon event-444"></span>
                <span data-i18n="events.444">4x4x4</span>
              </div>
              <div class="event-option" data-event="555">
                <span class="cubing-icon event-555"></span>
                <span data-i18n="events.555">5x5x5</span>
              </div>
              <div class="event-option" data-event="666">
                <span class="cubing-icon event-666"></span>
                <span data-i18n="events.666">6x6x6</span>
              </div>
              <div class="event-option" data-event="777">
                <span class="cubing-icon event-777"></span>
                <span data-i18n="events.777">7x7x7</span>
              </div>
              <div class="event-option" data-event="333bf">
                <span class="cubing-icon event-333bf"></span>
                <span data-i18n="events.333bf">3x3x3 Blindfolded</span>
              </div>
              <div class="event-option" data-event="333fm">
                <span class="cubing-icon event-333fm"></span>
                <span data-i18n="events.333fm">3x3x3 Fewest Moves</span>
              </div>
              <div class="event-option" data-event="333oh">
                <span class="cubing-icon event-333oh"></span>
                <span data-i18n="events.333oh">3x3x3 One-Handed</span>
              </div>
              <div class="event-option" data-event="clock">
                <span class="cubing-icon event-clock"></span>
                <span data-i18n="events.clock">Clock</span>
              </div>
              <div class="event-option" data-event="minx">
                <span class="cubing-icon event-minx"></span>
                <span data-i18n="events.minx">Megaminx</span>
              </div>
              <div class="event-option" data-event="pyram">
                <span class="cubing-icon event-pyram"></span>
                <span data-i18n="events.pyram">Pyraminx</span>
              </div>
              <div class="event-option" data-event="skewb">
                <span class="cubing-icon event-skewb"></span>
                <span data-i18n="events.skewb">Skewb</span>
              </div>
              <div class="event-option" data-event="sq1">
                <span class="cubing-icon event-sq1"></span>
                <span data-i18n="events.sq1">Square-1</span>
              </div>
              <div class="event-option" data-event="444bf">
                <span class="cubing-icon event-444bf"></span>
                <span data-i18n="events.444bf">4x4x4 Blindfolded</span>
              </div>
              <div class="event-option" data-event="555bf">
                <span class="cubing-icon event-555bf"></span>
                <span data-i18n="events.555bf">5x5x5 Blindfolded</span>
              </div>
              <div class="event-option" data-event="333mbf">
                <span class="cubing-icon event-333mbf"></span>
                <span data-i18n="events.333mbf">3x3x3 Multi-Blind</span>
              </div>
              <div class="event-divider"></div>
              <div
                class="event-option new-session-option"
                data-action="new-session"
              >
                <span class="fas fa-plus-circle"></span>
                <span data-i18n="sessions.newSession">New Session</span>
              </div>
            </div>
          </div>
          <button
            type="button"
            id="new-scramble"
            class="action-button"
            title="Next"
          >
            <i class="fas fa-random"></i>
          </button>
        </div>
        <div class="header-right">
          <button
            type="button"
            id="settings-btn"
            class="settings-button"
            title="Settings"
          >
            <i class="fas fa-cog"></i>
          </button>
        </div>
      </header>

      <!-- Main content area -->
      <div class="main-content">
        <!-- Scramble section -->
        <div class="scramble-section">
          <div class="scramble" id="scramble">
            <div class="scramble-loader" id="scramble-loader"></div>
            <span id="scramble-text" class="hidden"></span>
          </div>
        </div>

        <!-- Timer -->
        <div class="timer-container">
          <div class="timer" id="timer">0.000</div>
          <div class="inspection" id="inspection"></div>

          <!-- Stackmat Status -->
          <div class="stackmat-status" id="stackmat-status">
            <div class="stackmat-connection" id="stackmat-connection">
              <i class="fas fa-plug"></i>
              <span
                id="stackmat-connection-text"
                data-i18n="stackmat.disconnected"
                >Disconnected</span
              >
            </div>
          </div>

          <!-- Manual Input Timer -->
          <div class="manual-input-container" id="manual-input-container">
            <div class="manual-input-wrapper">
              <div class="input-container">
                <label for="manual-time-input" class="sr-only"
                  >Enter solve time manually</label
                >
                <input
                  type="text"
                  id="manual-time-input"
                  class="manual-time-input"
                  aria-label="Enter solve time manually"
                  placeholder="Enter time"
                />
                <div class="manual-inspection" id="manual-inspection"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Stats -->
        <div class="stats-container" id="stats-container">
          <!-- Statistics title removed -->
          <div class="stats">
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.solves">Solves:</div>
              <div class="stat-value" id="solve-count">0</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.best">Best:</div>
              <div class="stat-value" id="best-time">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.mean">Mean:</div>
              <div class="stat-value" id="mean-time">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg5">ao5:</div>
              <div class="stat-value" id="avg5">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg12">ao12:</div>
              <div class="stat-value" id="avg12">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg100">ao100:</div>
              <div class="stat-value" id="avg100">-</div>
            </div>
          </div>
        </div>

        <!-- Visualization -->
        <div class="visualization" id="visualization-container">
          <!-- Twisty player will be inserted here -->
        </div>
      </div>

      <!-- Times panel moved outside container -->

      <!-- Settings modal -->
      <div class="settings-modal" id="settings-modal">
        <div class="settings-content">
          <div class="settings-header">
            <div class="settings-title" data-i18n="settings.title">
              Settings
            </div>
            <button
              type="button"
              class="settings-close"
              id="settings-close"
              title="Close"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="settings-body">
            <div class="settings-section">
              <div
                class="settings-section-title"
                data-i18n="timerOptions.title"
              >
                Timer Options
              </div>
              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="use-inspection" checked />
                  <span data-i18n="timerOptions.useInspection"
                    >Use WCA Inspection (15s)</span
                  >
                </label>
              </div>
              <div class="settings-option">
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.inspectionSound"
                    >Inspection Sound:</span
                  >
                  <select
                    id="inspection-sound-selector"
                    class="inspection-sound-selector"
                  >
                    <option
                      value="none"
                      data-i18n="timerOptions.inspectionSoundNone"
                    >
                      None
                    </option>
                    <option
                      value="voice"
                      data-i18n="timerOptions.inspectionSoundVoice"
                    >
                      Voice
                    </option>
                    <option
                      value="beep"
                      data-i18n="timerOptions.inspectionSoundBeep"
                      selected
                    >
                      Beep
                    </option>
                  </select>
                </label>
              </div>
              <div class="settings-option">
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.timerMode">Timer Mode:</span>
                  <select id="timer-mode-selector" class="timer-mode-selector">
                    <option
                      value="timer"
                      data-i18n="timerOptions.timerModeTimer"
                    >
                      Timer
                    </option>
                    <option
                      value="typing"
                      data-i18n="timerOptions.timerModeTyping"
                    >
                      Typing
                    </option>
                    <option
                      value="stackmat"
                      data-i18n="timerOptions.timerModeStackmat"
                    >
                      Stackmat
                    </option>
                    <option
                      value="bluetooth"
                      data-i18n="timerOptions.timerModeBluetooth"
                      disabled
                    >
                      Bluetooth (Coming Soon)
                    </option>
                  </select>
                </label>
              </div>

              <div
                class="settings-option stackmat-only-setting"
                style="display: none"
              >
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.microphoneInput"
                    >Microphone Input:</span
                  >
                  <select id="microphone-selector" class="microphone-selector">
                    <option
                      value="auto"
                      data-i18n="timerOptions.microphoneAuto"
                    >
                      Auto-detect
                    </option>
                    <!-- Microphone options will be populated dynamically -->
                  </select>
                </label>
                <div class="setting-note">
                  <span data-i18n="timerOptions.microphoneNote"
                    >Choose your Y splitter or external microphone</span
                  >
                </div>
              </div>
              <div
                class="settings-option stackmat-only-setting"
                style="display: none"
              >
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    id="stackmat-reset-inspection"
                    checked
                  />
                  <span data-i18n="timerOptions.stackmatResetInspection"
                    >Stackmat Reset Triggers Inspection</span
                  >
                </label>
                <div class="setting-note">
                  <span data-i18n="timerOptions.stackmatResetNote"
                    >Note: Works after running the timer first</span
                  >
                </div>
              </div>
              <div class="settings-option">
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.decimalPlaces"
                    >Decimal Places:</span
                  >
                  <select
                    id="decimal-places-selector"
                    class="decimal-places-selector"
                  >
                    <option
                      value="0"
                      data-i18n="timerOptions.decimalPlacesNone"
                    >
                      None (12)
                    </option>
                    <option value="1" data-i18n="timerOptions.decimalPlaces1">
                      1 (12.3)
                    </option>
                    <option value="2" data-i18n="timerOptions.decimalPlaces2">
                      2 (12.34)
                    </option>
                    <option
                      value="3"
                      data-i18n="timerOptions.decimalPlaces3"
                      selected
                    >
                      3 (12.345)
                    </option>
                  </select>
                </label>
              </div>
            </div>
            <div class="settings-section">
              <div
                class="settings-section-title"
                data-i18n="displayOptions.title"
              >
                Display Options
              </div>
              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="show-visualization" checked />
                  <span data-i18n="displayOptions.showVisualization"
                    >Show Puzzle Visualization</span
                  >
                </label>
              </div>
              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="show-stats" checked />
                  <span data-i18n="displayOptions.showStats"
                    >Show Statistics</span
                  >
                </label>
              </div>

              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="dark-mode" />
                  <span data-i18n="displayOptions.darkMode">Dark Mode</span>
                </label>
              </div>

              <div class="settings-option slider-option">
                <label class="slider-label">
                  <span
                    class="scramble-font-size-text"
                    data-i18n="displayOptions.scrambleFontSize"
                    >Scramble Font Size</span
                  >
                  <div class="slider-container">
                    <input
                      type="range"
                      id="scramble-font-size"
                      min="0.1"
                      max="1.5"
                      step="0.1"
                      value="1.0"
                    />
                    <div class="slider-value" id="scramble-font-size-value">
                      1.0×
                    </div>
                  </div>
                </label>
              </div>
            </div>
            <div class="settings-section">
              <div class="settings-section-title" data-i18n="settings.language">
                Language
              </div>
              <div class="settings-option">
                <select
                  id="language-selector"
                  class="language-selector"
                  aria-label="Select language"
                >
                  <option value="en">English</option>
                  <option value="ar">العربية</option>
                  <option value="ckb">کوردی سۆرانی</option>
                </select>
              </div>
            </div>
          </div>
          <div class="settings-footer">
            <button type="button" id="settings-save" class="action-button">
              <i class="fas fa-check"></i>
              <span data-i18n="settings.save">Save</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Debug info -->
      <div class="debug-info" id="debug-info">
        <div>
          <span data-i18n="debug.timerState">Timer State:</span>
          <span id="timer-state">IDLE</span>
        </div>
        <div>
          <span data-i18n="debug.spaceHeldFor">Space Held For:</span>
          <span id="space-held-time">0</span>ms
        </div>
        <div>
          <span data-i18n="debug.currentEvent">Current Event:</span>
          <span id="current-event">333</span>
        </div>
        <div>
          <span data-i18n="debug.scrambleSource">Scramble Source:</span>
          <span id="scramble-source">-</span>
        </div>
      </div>

      <!-- Solve Details Modal -->
      <div class="solve-details-modal" id="solve-details-modal">
        <div class="solve-details-content">
          <div class="solve-details-header">
            <div class="solve-details-title" data-i18n="solveDetails.title">
              Solve Details
            </div>
            <button
              type="button"
              class="solve-details-close"
              id="solve-details-close"
              title="Close"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="solve-details-body">
            <div class="solve-details-time">
              <span id="solve-details-time-value">0.000</span>
            </div>
            <div class="solve-details-date">
              <span id="solve-details-date-value">Date</span>
            </div>
            <div class="solve-details-section">
              <div class="solve-details-section-header">
                <div
                  class="solve-details-section-title"
                  data-i18n="solveDetails.scramble"
                >
                  Scramble
                </div>
                <button
                  type="button"
                  class="copy-scramble-btn"
                  id="copy-scramble-btn"
                  title="Copy scramble"
                  data-i18n-title="solveDetails.copyScramble"
                >
                  <i class="fas fa-copy"></i>
                </button>
              </div>
              <div
                class="solve-details-scramble"
                id="solve-details-scramble"
              ></div>
            </div>
            <div class="solve-details-section">
              <div
                class="solve-details-section-title"
                data-i18n="solveDetails.penalty"
              >
                Penalty
              </div>
              <div class="solve-details-penalty">
                <label class="radio-label">
                  <input type="radio" name="penalty" value="none" checked />
                  <span data-i18n="solveDetails.none">None</span>
                </label>
                <label class="radio-label">
                  <input type="radio" name="penalty" value="+2" />
                  <span data-i18n="solveDetails.plusTwo">+2</span>
                </label>
                <label class="radio-label">
                  <input type="radio" name="penalty" value="dnf" />
                  <span data-i18n="solveDetails.dnf">DNF</span>
                </label>
              </div>
            </div>
            <div class="solve-details-section">
              <div
                class="solve-details-section-title"
                data-i18n="solveDetails.comment"
              >
                Comment
              </div>
              <textarea
                id="solve-details-comment"
                placeholder="Add a comment..."
                data-i18n-placeholder="solveDetails.addComment"
              ></textarea>
            </div>
          </div>
          <div class="solve-details-footer">
            <button type="button" id="solve-details-save" class="action-button">
              <i class="fas fa-check"></i>
              <span data-i18n="solveDetails.save">Save</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- MBLD Cube Count Modal -->
    <div class="mbld-modal" id="mbld-cube-count-modal">
      <div class="mbld-modal-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.setup">
            Multi-Blind Setup
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-cube-count-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body">
          <div class="mbld-input-group">
            <label for="mbld-cube-count" data-i18n="mbld.numberOfCubesMinimum"
              >Number of cubes (minimum 2):</label
            >
            <input
              type="number"
              id="mbld-cube-count"
              min="2"
              value="2"
              class="mbld-input"
            />
          </div>
        </div>
        <div class="mbld-modal-footer">
          <button type="button" id="mbld-cube-count-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="mbld.generateScrambles">Generate Scrambles</span>
          </button>
        </div>
      </div>
    </div>

    <!-- MBLD Scrambles Modal -->
    <div class="mbld-modal" id="mbld-scrambles-modal">
      <div class="mbld-modal-content mbld-scrambles-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.scrambles">
            Multi-Blind Scrambles
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-scrambles-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body" id="mbld-scrambles-container">
          <!-- Scrambles will be inserted here -->
        </div>
      </div>
    </div>

    <!-- MBLD Visualizations Modal -->
    <div class="mbld-modal" id="mbld-visualizations-modal">
      <div class="mbld-modal-content mbld-visualizations-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.visualizations">
            Multi-Blind Visualizations
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-visualizations-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body" id="mbld-visualizations-container">
          <!-- Visualizations will be inserted here -->
        </div>
      </div>
    </div>

    <!-- MBLD Results Modal -->
    <div class="mbld-modal" id="mbld-results-modal">
      <div class="mbld-modal-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.results">
            Multi-Blind Results
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-results-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body">
          <div id="mbld-results-info" class="mbld-info"></div>
          <div class="mbld-input-group">
            <label for="mbld-solved-count" data-i18n="mbld.numberOfCubesSolved"
              >Number of cubes solved:</label
            >
            <input
              type="number"
              id="mbld-solved-count"
              min="0"
              value="0"
              class="mbld-input"
            />
          </div>
          <div class="mbld-input-group">
            <label for="mbld-total-count" data-i18n="app.outOf">Out of:</label>
            <input
              type="number"
              id="mbld-total-count"
              min="2"
              value="2"
              class="mbld-input"
              readonly
            />
          </div>
        </div>
        <div class="mbld-modal-footer">
          <button type="button" id="mbld-results-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="mbld.saveResult">Save Result</span>
          </button>
        </div>
      </div>
    </div>

    <!-- New Session Modal -->
    <div class="modal" id="new-session-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title" data-i18n="sessions.newSessionTitle">
            New Session
          </div>
          <button
            type="button"
            class="modal-close"
            id="new-session-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="input-group">
            <label for="session-name" data-i18n="sessions.sessionName"
              >Session Name:</label
            >
            <input
              type="text"
              id="session-name"
              class="session-input"
              placeholder="My Session"
              data-i18n-placeholder="sessions.sessionNamePlaceholder"
            />
          </div>
          <div class="input-group">
            <label for="session-puzzle" data-i18n="sessions.puzzleType"
              >Puzzle Type:</label
            >
            <select id="session-puzzle" class="session-input">
              <optgroup label="WCA Puzzles">
                <option value="222" data-i18n="events.222">2x2x2</option>
                <option value="333" data-i18n="events.333" selected>
                  3x3x3
                </option>
                <option value="444" data-i18n="events.444">4x4x4</option>
                <option value="555" data-i18n="events.555">5x5x5</option>
                <option value="666" data-i18n="events.666">6x6x6</option>
                <option value="777" data-i18n="events.777">7x7x7</option>
                <option value="333oh" data-i18n="events.333oh">
                  3x3x3 One-Handed
                </option>
                <option value="clock" data-i18n="events.clock">Clock</option>
                <option value="minx" data-i18n="events.minx">Megaminx</option>
                <option value="pyram" data-i18n="events.pyram">Pyraminx</option>
                <option value="skewb" data-i18n="events.skewb">Skewb</option>
                <option value="sq1" data-i18n="events.sq1">Square-1</option>
              </optgroup>
              <optgroup label="Non-WCA Puzzles">
                <option value="fto">Face-Turning Octahedron</option>
                <option value="master_tetraminx">Master Tetraminx</option>
                <option value="kilominx">Kilominx</option>
                <option value="redi_cube">Redi Cube</option>
                <option value="baby_fto">Baby FTO</option>
              </optgroup>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" id="new-session-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="sessions.create">Create</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Edit Session Modal -->
    <div class="modal" id="edit-session-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title" data-i18n="sessions.editSessionTitle">
            Edit Session
          </div>
          <button
            type="button"
            class="modal-close"
            id="edit-session-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="input-group">
            <label for="edit-session-name" data-i18n="sessions.sessionName"
              >Session Name:</label
            >
            <input
              type="text"
              id="edit-session-name"
              class="session-input"
              placeholder="My Session"
              data-i18n-placeholder="sessions.sessionNamePlaceholder"
            />
          </div>
          <div class="input-group">
            <label for="edit-session-puzzle" data-i18n="sessions.puzzleType"
              >Puzzle Type:</label
            >
            <select id="edit-session-puzzle" class="session-input">
              <optgroup label="WCA Puzzles">
                <option value="222" data-i18n="events.222">2x2x2</option>
                <option value="333" data-i18n="events.333">3x3x3</option>
                <option value="444" data-i18n="events.444">4x4x4</option>
                <option value="555" data-i18n="events.555">5x5x5</option>
                <option value="666" data-i18n="events.666">6x6x6</option>
                <option value="777" data-i18n="events.777">7x7x7</option>
                <option value="333oh" data-i18n="events.333oh">
                  3x3x3 One-Handed
                </option>
                <option value="clock" data-i18n="events.clock">Clock</option>
                <option value="minx" data-i18n="events.minx">Megaminx</option>
                <option value="pyram" data-i18n="events.pyram">Pyraminx</option>
                <option value="skewb" data-i18n="events.skewb">Skewb</option>
                <option value="sq1" data-i18n="events.sq1">Square-1</option>
              </optgroup>
              <optgroup label="Non-WCA Puzzles">
                <option value="fto">Face-Turning Octahedron</option>
                <option value="master_tetraminx">Master Tetraminx</option>
                <option value="kilominx">Kilominx</option>
                <option value="redi_cube">Redi Cube</option>
                <option value="baby_fto">Baby FTO</option>
              </optgroup>
            </select>
          </div>
          <input type="hidden" id="edit-session-id" />
        </div>
        <div class="modal-footer">
          <div class="modal-footer-buttons">
            <button type="button" id="edit-session-save" class="action-button">
              <i class="fas fa-check"></i>
              <span data-i18n="sessions.save">Save</span>
            </button>
            <button
              type="button"
              id="edit-session-delete"
              class="delete-button"
            >
              <i class="fas fa-trash-alt"></i>
              <span data-i18n="sessions.delete">Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Details Panel -->
    <div class="stats-details-panel" id="stats-details-panel">
      <div class="stats-details-header">
        <div class="stats-details-title-container">
          <span
            class="stats-details-title-text"
            data-i18n="statsDetails.titleFor"
            >Statistics Details for</span
          >
          <div class="stats-event-selector">
            <button
              type="button"
              class="stats-event-selector-btn"
              id="stats-event-selector-btn"
              title="Select Event/Session"
            >
              <span class="cubing-icon event-333" id="stats-event-icon"></span>
              <span class="stats-event-text" id="stats-event-text">3x3x3</span>
            </button>
            <div class="stats-event-dropdown" id="stats-event-dropdown">
              <!-- Options will be populated dynamically -->
            </div>
          </div>
        </div>
        <button
          type="button"
          class="stats-details-close"
          id="stats-details-close"
          title="Close Statistics"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="stats-details-content">
        <!-- Single Event/Session View -->
        <div id="single-stats-view">
          <!-- Row 1: Overview and Averages -->
          <div class="stats-row">
            <!-- Overview Section -->
            <div class="stats-section">
              <h3 class="stats-section-title" data-i18n="statsDetails.overview">
                Overview
              </h3>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.solves">
                    Solves
                  </div>
                  <div class="stat-card-value" id="detail-solve-count">0</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.best">Best</div>
                  <div class="stat-card-value" id="detail-best-time">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.worst">
                    Worst
                  </div>
                  <div class="stat-card-value" id="detail-worst-time">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.mean">Mean</div>
                  <div class="stat-card-value" id="detail-mean-time">-</div>
                </div>
              </div>
            </div>

            <!-- Averages Section -->
            <div class="stats-section">
              <h3 class="stats-section-title" data-i18n="statsDetails.averages">
                Averages
              </h3>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg5">ao5</div>
                  <div class="stat-card-value" id="detail-avg5">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg12">
                    ao12
                  </div>
                  <div class="stat-card-value" id="detail-avg12">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg100">
                    ao100
                  </div>
                  <div class="stat-card-value" id="detail-avg100">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg1000">
                    ao1000
                  </div>
                  <div class="stat-card-value" id="detail-avg1000">-</div>
                </div>
                <div class="stat-card">
                  <div
                    class="stat-card-label"
                    data-i18n="statsDetails.standardDeviation"
                  >
                    Std Dev
                  </div>
                  <div class="stat-card-value" id="detail-std-dev">-</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Row 2: Records -->
          <div class="stats-row">
            <div class="stats-section">
              <h3 class="stats-section-title" data-i18n="statsDetails.records">
                Records
              </h3>
              <div class="records-list">
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestSingle">
                    Best Single:
                  </div>
                  <div class="record-value" id="record-best-single">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo5">
                    Best ao5:
                  </div>
                  <div class="record-value" id="record-best-ao5">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo12">
                    Best ao12:
                  </div>
                  <div class="record-value" id="record-best-ao12">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo100">
                    Best ao100:
                  </div>
                  <div class="record-value" id="record-best-ao100">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo1000">
                    Best ao1000:
                  </div>
                  <div class="record-value" id="record-best-ao1000">-</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Row 3: Charts -->
          <div class="stats-row">
            <!-- Time Distribution Chart -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.timeDistribution"
              >
                Time Distribution
              </h3>
              <div class="chart-container">
                <canvas
                  id="time-distribution-chart"
                  width="400"
                  height="200"
                ></canvas>
              </div>
            </div>

            <!-- Progress Chart -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.progressChart"
              >
                Progress Over Time
              </h3>
              <div class="chart-container">
                <canvas id="progress-chart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>

          <!-- Row 4: Session Analysis and Predictions -->
          <div class="stats-row">
            <!-- Session Analysis -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.sessionAnalysis"
              >
                Session Analysis
              </h3>
              <div class="analysis-grid">
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.totalTime"
                  >
                    Total Time:
                  </div>
                  <div class="analysis-value" id="analysis-total-time">-</div>
                </div>
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.averageTime"
                  >
                    Average Time:
                  </div>
                  <div class="analysis-value" id="analysis-average-time">-</div>
                </div>
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.solvesPerHour"
                  >
                    Solves/Hour:
                  </div>
                  <div class="analysis-value" id="analysis-solves-per-hour">
                    -
                  </div>
                </div>
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.consistency"
                  >
                    Consistency:
                  </div>
                  <div class="analysis-value" id="analysis-consistency">-</div>
                </div>
              </div>
            </div>

            <!-- Future Expectations -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.predictions"
              >
                Predictions
              </h3>
              <div class="predictions-grid">
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.nextAo5"
                  >
                    Next ao5 Target:
                  </div>
                  <div class="prediction-value" id="prediction-ao5">-</div>
                </div>
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.nextAo12"
                  >
                    Next ao12 Target:
                  </div>
                  <div class="prediction-value" id="prediction-ao12">-</div>
                </div>
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.improvementRate"
                  >
                    Improvement Rate:
                  </div>
                  <div class="prediction-value" id="prediction-improvement">
                    -
                  </div>
                </div>
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.targetTime"
                  >
                    Target Time:
                  </div>
                  <div class="prediction-value" id="prediction-target">-</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- All Events Overview -->
        <div id="all-events-view" style="display: none">
          <div class="all-events-list" id="all-events-list">
            <!-- Events will be populated here -->
          </div>
        </div>

        <!-- Row 5: Import/Export and Session Management -->
        <div class="stats-row">
          <div class="stats-import-export-section">
            <div class="stats-section-title">Import/Export</div>
            <div class="import-export-buttons">
              <button
                type="button"
                class="import-export-btn"
                id="import-times-btn"
              >
                <i class="fas fa-file-import"></i>
                <span data-i18n="statsDetails.importTimes">Import Times</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="export-times-btn"
              >
                <i class="fas fa-file-export"></i>
                <span data-i18n="statsDetails.exportTimes">Export Times</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="stats-edit-session-btn"
                style="display: none"
              >
                <i class="fas fa-edit"></i>
                <span data-i18n="statsDetails.editSession">Edit Session</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="stats-empty-session-btn"
                style="display: none"
              >
                <i class="fas fa-trash"></i>
                <span data-i18n="statsDetails.emptySession">Empty Session</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stat Detail Modal -->
    <div class="modal" id="stat-detail-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title" id="stat-detail-title">
            Statistic Details
          </div>
          <button
            type="button"
            class="modal-close"
            id="stat-detail-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="stat-detail-info">
            <div class="stat-detail-item">
              <span class="stat-detail-label">Time:</span>
              <span class="stat-detail-value" id="stat-detail-value">-</span>
            </div>
            <div class="stat-detail-item" id="stat-detail-datetime-item">
              <span class="stat-detail-label" id="stat-detail-datetime-label"
                >Date & Time:</span
              >
              <span class="stat-detail-value" id="stat-detail-datetime">-</span>
            </div>
          </div>
          <div
            class="stat-detail-scramble-section"
            id="stat-detail-single-scramble-section"
          >
            <div class="stat-detail-scramble-header">
              <span class="stat-detail-label">Scramble:</span>
              <button
                type="button"
                class="copy-scramble-btn"
                id="stat-detail-copy-scramble"
                title="Copy scramble"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="stat-detail-scramble" id="stat-detail-scramble">-</div>
          </div>
          <div
            class="stat-detail-average-info"
            id="stat-detail-average-info"
            style="display: none"
          >
            <div class="stat-detail-duration-info">
              <div class="stat-detail-item stat-detail-datetime-range">
                <span class="stat-detail-label">Date & Time:</span>
                <span class="stat-detail-value" id="stat-detail-datetime-range"
                  >-</span
                >
              </div>
            </div>
            <div class="stat-detail-scrambles-section">
              <div class="stat-detail-label">Scrambles & Times:</div>
              <div
                class="stat-detail-scrambles-list"
                id="stat-detail-scrambles-list"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Times toggle button (outside all containers) -->
    <button
      type="button"
      class="times-toggle"
      id="times-toggle"
      title="View Times"
    >
      <i class="fas fa-history"></i>
    </button>

    <!-- Audio elements for inspection sounds -->
    <audio id="beep-sound" preload="auto">
      <source src="assets/sounds/8sec.wav" type="audio/wav" />
    </audio>
    <audio id="double-beep-sound" preload="auto">
      <source src="assets/sounds/12sec.wav" type="audio/wav" />
    </audio>

    <!-- Audio elements are used by the main code -->

    <!-- Audio elements are kept for fallback compatibility -->

    <!-- Stackmat Timer Library -->
    <script src="stackmat-main/dist/umd/stackmat.min.js"></script>
    <script src="js/stackmat-manager.js"></script>

    <!-- Custom JavaScript -->
    <script type="module">
      // Import the language manager
      import { initLanguageManager } from "./js/language-manager.js";

      // Initialize the language manager
      document.addEventListener("DOMContentLoaded", async () => {
        await initLanguageManager();
      });
    </script>
    <script type="module" src="js/cubing-timer.js"></script>
    <!-- MBLD Manager is imported by cubing-timer.js -->

    <!-- Service Worker Registration -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("service-worker.js?v=" + new Date().getTime())
            .then(() => {
              // Registration successful
            })
            .catch(() => {
              // Registration failed
            });
        });
      }
    </script>
  </body>
</html>
