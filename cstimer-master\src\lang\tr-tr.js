var OK_LANG = 'Tamam';
var CANCEL_LANG = 'İptal';
var RESET_LANG = 'Sıfırla';
var ABOUT_LANG = 'Hakkında';
var ZOOM_LANG = 'Yakınlaştır';
var COPY_LANG = 'Kopyala';
var BUTTON_TIME_LIST = 'Süre<br>listesi';
var BUTTON_OPTIONS = 'Ayarlar';
var BUTTON_EXPORT = 'Dışa aktar';
var BUTTON_DONATE = 'Bağış yap';
var PROPERTY_SR = 'Bu oturumda';
var PROPERTY_USEINS = 'WCA inecelemesi kullan';
var PROPERTY_USEINS_STR = 'Her zaman (aşağı)|Her zaman (yukarı)|BLD hariç (aşağı)|BLD hariç (yukarı)|Asla';
var PROPERTY_SHOWINS = 'İnceleme etkinken ikon göster';
var PROPERTY_VOICEINS = 'WCA incelemesi ses uyarısı';
var PROPERTY_VOICEINS_STR = 'hiçbiri|erkek sesi|kadın sesi';
var PROPERTY_VOICEVOL = 'Ses düzeyi';
var PROPERTY_PHASES = 'çok zamanlı';
var PROPERTY_TIMERSIZE = 'kronometre boyutu';
var PROPERTY_USEMILLI = 'milisaniye kullan';
var PROPERTY_SMALLADP = 'noktadan sonra küçük karakter kullan';
var PROPERTY_SCRSIZE = 'karıştırma bouyutu';
var PROPERTY_SCRMONO = 'eş aralıklı karıştırma';
var PROPERTY_SCRLIM = 'karıştırma bölgesinin yüksekliğini limitle';
var PROPERTY_SCRALIGN = 'Karıştırma bölgesi hizası';
var PROPERTY_SCRALIGN_STR = 'merkez|sol|sağ';
var PROPERTY_SCRWRAP = 'Karıştırma Sargısı';
var PROPERTY_SCRWRAP_STR = 'Dengeli|Normal';
var PROPERTY_SCRNEUT = 'Renk nötr';
var PROPERTY_SCRNEUT_STR = 'Yok|Tek yüz|Çift yüz|Altı yüz';
var PROPERTY_SCREQPR = 'Eğitim-karışıklık durumları için olasılıklar';
var PROPERTY_SCREQPR_STR = 'Gerçek|Eşit|Rastgele sıra';
var PROPERTY_SCRFAST = '4x4x4 için hızlı karışım kullanma (resmi değil)';
var PROPERTY_SCRKEYM = 'Karıştırmada anahtar hareket(ler)ini etiketleyin';
var PROPERTY_SCRCLK = 'Karıştır\'a tıklandığında eylem';
var PROPERTY_SCRCLK_STR = 'Hiçbiri|Kopyala|Sonraki karıştırma';
var PROPERTY_WNDSCR = 'Karıştırma paneli görünüm stili';
var PROPERTY_WNDSTAT = 'Karıştırma paneli görünüm stili';
var PROPERTY_WNDTOOL = 'Aletler Paneli Görünüm Stili';
var PROPERTY_WND_STR = 'Normal|Düz';
var EXPORT_DATAEXPORT = 'veri içe aktar/dışa aktar';
var EXPORT_TOFILE = 'dosyaya aktar';
var EXPORT_FROMFILE = 'dosyadan aktar';
var EXPORT_TOSERV = 'servera aktar';
var EXPORT_FROMSERV = 'serverdan aktar';
var EXPORT_FROMOTHER = 'Diğer zamanlayıcılardan oturumları içe aktar';
var EXPORT_USERID = 'Lütfen hesabınızı girin (sadece harf veya numara)';
var EXPORT_INVID = 'sadece harf veya numara kullanılabilir';
var EXPORT_ERROR = 'Bir hata oluştu...';
var EXPORT_NODATA = 'Hesabınız için veri bulunamadı';
var EXPORT_UPLOADED = 'Yükleme başarılı';
var EXPORT_CODEPROMPT = 'Bu kodu kaydedin veya içe aktarmak için kayıtlı kodu yazın';
var EXPORT_ONLYOPT = 'Sadece Ayarları Dışa Aktar/İçe Aktar';
var EXPORT_ACCOUNT = 'Hesapları dışa aktar';
var EXPORT_LOGINGGL = 'Google hesabı ile Giriş yap';
var EXPORT_LOGINWCA = 'WCA hesabı ile Giriş yap';
var EXPORT_LOGOUTCFM = 'Çıkış yapmak istiyor musunuz?';
var EXPORT_LOGINAUTHED = 'Yetkilendirildi<br>Veriler Alınıyor...';
var EXPORT_AEXPALERT = 'More than %d solves since last backup';
var EXPORT_WHICH = '%d dosyanız var, hangisinin içe aktarılması gerekiyor?';
var EXPORT_WHICH_ITEM = '%s çözüm(ler), %t tarihinde yüklendi';
var IMPORT_FINAL_CONFIRM = 'Bu tüm yerel verileri geçersiz kılacaktır! En azından %d oturumlar değiştirecek, %a ekleyecek ve %r çözümler kaldıracaktır. Verileri içe aktarmayı onaylıyor musunuz?';
var BUTTON_SCRAMBLE = 'karış-<br>tırma';
var BUTTON_TOOLS = 'Yardımcılar';
var IMAGE_UNAVAILABLE = 'Bu karıştırma türü ile uyumlu değil';
var TOOLS_SELECTFUNC = 'kullanım';
var TOOLS_CROSS = 'cross çözümü';
var TOOLS_EOLINE = 'EO Line çözümü';
var TOOLS_ROUX1 = 'Roux S1 çözümü';
var TOOLS_222FACE = '2x2x2 Yüzü';
var TOOLS_GIIKER = 'Bluetooth Kübü';
var TOOLS_IMAGE = 'karıştırmayı göster';
var TOOLS_STATS = 'İstatistikler';
var TOOLS_HUGESTATS = 'Haç-Oturum istatistikler';
var TOOLS_DISTRIBUTION = 'Süre dağılımı';
var TOOLS_TREND = 'Süre grafiği';
var TOOLS_METRONOME = 'Metronom';
var TOOLS_RECONS = 'Çözümleme';
var TOOLS_RECONS_NODATA = 'Çözüm bulunamadı.';
var TOOLS_RECONS_TITLE = 'insp|exec|turn|tps';
var TOOLS_TRAINSTAT = 'Antrenman İstatistikleri';
var TOOLS_BLDHELPER = 'Gözü Bağlı Yardımcısı';
var TOOLS_CFMTIME = 'Süreyi doğrula';
var TOOLS_SOLVERS = 'Çözücüler';
var TOOLS_DLYSTAT = 'Günlük İstatistikler';
var TOOLS_DLYSTAT1 = 'Periyot|Gün Başlangıcı|Hafta';
var TOOLS_DLYSTAT_OPT1 = 'Gün|Hafta|Ay|Yıl';
var TOOLS_DLYSTAT_OPT2 = 'Paz|Pzt|Sal|Çrş|Prş|Cum|Cmt';
var TOOLS_SYNCSEED = 'Yaygın Karışım';
var TOOLS_SYNCSEED_SEED = 'Tohum';
var TOOLS_SYNCSEED_INPUT = 'Giriş Tohumu';
var TOOLS_SYNCSEED_30S = 'Use 30s Seed';
var TOOLS_SYNCSEED_HELP = 'If enabled, scramble will only depend on the seed and scramble settings.';
var TOOLS_SYNCSEED_DISABLE = 'Disable current seed?';
var TOOLS_SYNCSEED_INPUTA = 'Input a value (a-zA-Z0-9) as seed';
var TOOLS_BATTLE = 'Çevrimiçi Mücadele';
var TOOLS_BATTLE_HEAD = 'Oda|Odaya Katıl';
var TOOLS_BATTLE_TITLE = 'Sıra|Durum|Zaman';
var TOOLS_BATTLE_STATUS = 'Hazır|İnceleme|Çözülüyor|Çözüldü|Kayıp';
var TOOLS_BATTLE_INFO = 'Arkadaşınızla mücadele odasına katılın, sonra birlikte mücadele edeceksiniz.';
var TOOLS_BATTLE_JOINALERT = 'Lütfen Oda ID\'si girin';
var TOOLS_BATTLE_LEAVEALERT = 'Mevcut odadan ayrıl';
var OLCOMP_UPDATELIST = 'Yarışma Listesini Yenile';
var OLCOMP_VIEWRESULT = 'Sonuçları Görüntüle';
var OLCOMP_VIEWMYRESULT = 'Geçmişim';
var OLCOMP_START = 'Başlat!';
var OLCOMP_SUBMIT = 'Gönder';
var OLCOMP_SUBMITAS = 'Olarak Gönder:';
var OLCOMP_WCANOTICE = 'WCA Hesabınız Olarak Gönder? (Gönderdikten sonra tanınmazsa yeniden giriş yapın)';
var OLCOMP_OLCOMP = 'Online Yarışma';
var OLCOMP_ANONYM = 'Anonim';
var OLCOMP_ME = 'Ben';
var OLCOMP_WCAACCOUNT = 'WCA hesabı';
var OLCOMP_ABORT = 'Yarışmayı iptal edip sonuçları mı gösterelim?';
var OLCOMP_WITHANONYM = 'Anonim Olarak';
var PROPERTY_IMGSIZE = 'Karıştırma resmi boyutu';
var PROPERTY_IMGREP = 'Karıştırılmış resme tıklandığında sanal küp animasyonunu göster';
var TIMER_INSPECT = 'incele';
var TIMER_SOLVE = 'çöz';
var PROPERTY_USEMOUSE = 'mouse kronometresi kullan';
var PROPERTY_TIMEU = 'kronometre güncellemesi';
var PROPERTY_TIMEU_STR = 'güncelle|0.1s|saniye|inceleme|hiçbiri';
var PROPERTY_PRETIME = 'boşluk tuşuna basılı tutma süresi(saniye)';
var PROPERTY_ENTERING = 'süreleri girmek için kullan';
var PROPERTY_ENTERING_STR = 'Kronometre|Yazarak|Stackmat|MoYuTimer|Sanal Küp|Bluetooth|qCube|GanTimer|Son katman çalışması';
var PROPERTY_INTUNIT = 'Süre giriş formatı';
var PROPERTY_INTUNIT_STR = 'saniye|santisaniye|milisaniye';
var PROPERTY_COLOR = 'renk teması seç';
var PROPERTY_COLORS = 'yazı rengi|arkaplan rengi|panel rengi|buton rengi|bağlantı rengi|Logo rengi|Logo arkaplan rengi';
var PROPERTY_VIEW = 'UI stili';
var PROPERTY_VIEW_STR = 'Otomatik|Mobil|Masaüstü';
var PROPERTY_UIDESIGN = 'Arayüz Tasarımı';
var PROPERTY_UIDESIGN_STR = 'Normal|Materyal Tasarım| Gölgesiz Normal| Gölgesiz Materyal Tasarım';
var COLOR_EXPORT = 'Lütfen içe aktarma için stringi koruyun';
var COLOR_IMPORT = 'Dışa aktarılan stringi girin';
var COLOR_FAIL = 'İçe aktarma başarısız oldu, yanlış veri';
var PROPERTY_FONTCOLOR_STR = 'siyah|beyaz';
var PROPERTY_COLOR_STR = 'manual|içe/dışa aktar...|rastgele|stil 1|stil 2|stil 3|siyah|beyaz|stil 6|solarized dark|solarized light';
var PROPERTY_FONT = 'kronometrenin yazı tipini seç';
var PROPERTY_FONT_STR = 'rastgele|normal|dijital1 1|dijital 2|dijital 3|dijital 4|dijital 5';
var PROPERTY_FORMAT = 'zaman biçimi';
var PROPERTY_USEKSC = 'kısayol kullan';
var PROPERTY_USEGES = 'hareket kontrolünü kullan';
var PROPERTY_NTOOLS = 'yardımcı sayısı';
var PROPERTY_AHIDE = 'Zaman tutulurken her şeyi gizle';
var SCRAMBLE_LAST = 'önceki';
var SCRAMBLE_NEXT = 'sonraki';
var SCRAMBLE_SCRAMBLE = 'karıştırma';
var SCRAMBLE_SCRAMBLING = 'Karıştırma';
var SCRAMBLE_LENGTH = 'uzunluk';
var SCRAMBLE_INPUT = 'karıştırma(lar) içe aktar';
var SCRAMBLE_INPUTTYPE = 'Karıştırma türü';
var PROPERTY_VRCSPEED = 'VRC temelli hız (tps)';
var PROPERTY_VRCORI = 'Sanal küp yönlendirmesi';
var PROPERTY_VRCMP = 'multi-evre';
var PROPERTY_VRCMPS = 'Yok|CFOP|CF+OP|CFFFFOP|CFFFFOOPP|Roux';
var PROPERTY_GIIKERVRC = 'Sanal bluetooth kübü göster';
var PROPERTY_GIISOK_DELAY = 'Beklerse Karıştırılmış Olarak işaretle';
var PROPERTY_GIISOK_DELAYS = '2s|3s|4s|5s|Hiçbirzaman|doğru karıştırılmış';
var PROPERTY_GIISOK_KEY = 'space basarak karıştırılmış olarak işaretle';
var PROPERTY_GIISOK_MOVE = 'yaparak karıştırılmış işaretle';
var PROPERTY_GIISOK_MOVES = 'U4, R4, vs.|(U U\') 2, (U\' U) 2, vs.|Hiçbirzaman';
var PROPERTY_GIISBEEP = 'karıştırma işaretlendiğinde beep yap';
var PROPERTY_GIIRST = 'bluetooth\'u kübü çözülmüş olarak resetle';
var PROPERTY_GIIRSTS = 'herzaman|komut istemi|hiçbirzaman';
var PROPERTY_GIIMODE = 'Bluetooth Küp Modu';
var PROPERTY_GIIMODES = 'Normal|Training|Continuous training';
var PROPERTY_VRCAH = 'Useless pieces in huge cube';
var PROPERTY_VRCAHS = 'Gizle|Kenar|Renk|Göster';
var CONFIRM_GIIRST = 'bluetooth\'u kübü çözülmüş olarak resetle';
var PROPERTY_GIIAED = 'otomatik donanım hatası algılama';
var scrdata = [
	['WCA', [
		['3x3x3', "333", 0],
		['2x2x2', "222so", 0],
		['4x4x4', "444wca", -40],
		['5x5x5', "555wca", -60],
		['6x6x6', "666wca", -80],
		['7x7x7', "777wca", -100],
		['3x3 gözü kapalı', "333ni", 0],
		['3x3 en az hamle', "333fm", 0],
		['3x3 tek el', "333oh", 0],
		['Saat', "clkwca", 0],
		['Megaminx', "mgmp", -70],
		['Pyraminx', "pyrso", -10],
		['Skewb', "skbso", 0],
		['kr1', "sqrs", 0],
		['4x4 gözü kapalı', "444bld", -40],
		['5x5 gözü kapalı', "555bld", -60],
		['3x3 çoklu gözü kapalı', "r3ni", 5]
	]],
	['İçe aktar', [
		['Dış', "input", 0],
		['Yarışma', "remoteComp", 0],
		['Çevrimiçi Mücadele', "remoteBattle", 0],
		['Remote', "remoteOther", 0]
	]],
	['===WCA===', [
		['--', "blank", 0]
	]],
	['3x3x3', [
		["Rastgele Durum (WCA)", "333", 0],
		['Rastgele hamle', "333o", 25],
		['yeniler için 3x3', "333noob", 25],
		['sadece kenarlar', "edges", 0],
		['sadece köşeler', "corners", 0],
		['Gözü Bağlı Yardımcısı', "nocache_333bldspec", 0],
		['Pattern Tool', "nocache_333patspec", 0],
		['3x3 ayakla', "333ft", 0],
		['Özel', "333custom", 0]
	]],
	['3x3x3 CFOP', [
		['PLL', "pll", 0],
		['OLL', "oll", 0],
		['son bölme + son katman', "lsll2", 0],
		['son katman', "ll", 0],
		['ZBLL', "zbll", 0],
		['COLL', "coll", 0],
		['CLL', "cll", 0],
		['ELL', "ell", 0],
		['2GLL', "2gll", 0],
		['ZZLL', "zzll", 0],
		['ZBLS', "zbls", 0],
		['EOLS', "eols", 0],
		['WVLS', "wvls", 0],
		['VLS', "vls", 0],
		['cross çözülü', "f2l", 0],
		['EO Çizgisi', "eoline", 0],
		['EO Cross', "eocross", 0],
		['kolay cross', "easyc", 3],
		['Kolay xcross', "easyxc", 4]
	]],
	['3x3x3 Roux', [
		['2nd Block', "sbrx", 0],
		['CMLL', "cmll", 0],
		['LSE', "lse", 0],
		['LSE &lt;M, U&gt;', "lsemu", 0]
	]],
	['3x3x3 Mehta', [
		['3QB', "mt3qb", 0],
		['EOLE', "mteole", 0],
		['TDR', "mttdr", 0],
		['6CP', "mt6cp", 0],
		['CDRLL', "mtcdrll", 0],
		['L5EP', "mtl5ep", 0],
		['TTLL', "ttll", 0]
	]],
	['2x2x2', [
		["Rastgele Durum (WCA)", "222so", 0],
		['ideal', "222o", 0],
		['3-tür', "2223", 25],
		['EG', "222eg", 0],
		['CLL', "222eg0", 0],
		['EG1', "222eg1", 0],
		['EG2', "222eg2", 0],
		['TCLL+', "222tcp", 0],
		['TCLL-', "222tcn", 0],
		['TCLL', "222tc", 0],
		['LS', "222lsall", 0],
		['Çubuk Yok', "222nb", 0]
	]],
	['4x4x4', [
		["WCA", "444wca", -40],
		['Rastgele hamle', "444m", 40],
		['işaret', "444", 40],
		['YJ', "444yj", 40],
		['4x4x4 kenarlar', "4edge", 0],
		['R,r,U,u', "RrUu", 40],
		['Last layer', "444ll", 0],
		['ELL', "444ell", 0],
		['Edge only', "444edo", 0],
		['Center only', "444cto", 0]
	]],
	['4x4x4 Yau/Hoya', [
		['UD center solved', "444ctud", 0],
		['UD+3E solved', "444ud3c", 0],
		['Last 8 dedges', "444l8e", 0],
		['RL center solved', "444ctrl", 0],
		['RLDX center solved', "444rlda", 0],
		['RLDX cross solved', "444rlca", 0]
	]],
	['5x5x5', [
		["WCA", "555wca", 60],
		['işaret', "555", 60],
		['5x5x5 kenarlar', "5edge", 8]
	]],
	['6x6x6', [
		["WCA", "666wca", 80],
		['işaret', "666si", 80],
		['Ön Düzeltme', "666p", 80],
		['suffix', "666s", 80],
		['6x6x6 kenarlar', "6edge", 8]
	]],
	['7x7x7', [
		["WCA", "777wca", 100],
		['işaret', "777si", 100],
		['Ön Düzeltme', "777p", 100],
		['suffix', "777s", 100],
		['7x7x7 kenarlar', "7edge", 8]
	]],
	['Saat', [
		['WCA', "clkwca", 0],
		['Dünya Küp Derneği (old)', "clkwcab", 0],
		['WCA w/o y2', "clknf", 0],
		['jaap', "clk", 0],
		['ideal', "clko", 0],
		['Özlü', "clkc", 0],
		['verimli düğme sırası', "clke", 0]
	]],
	['Megaminx', [
		["WCA", "mgmp", 70],
		['havuç', "mgmc", 70],
		['eski stil', "mgmo", 70],
		['2-tür hamle R,U', "minx2g", 30],
		['son bölme + son katman', "mlsll", 0],
		['PLL', "mgmpll", 0],
		['Last Layer', "mgmll", 0]
	]],
	['Pyraminx', [
		["Rastgele Durum (WCA)", "pyrso", 10],
		['ideal', "pyro", 0],
		['Rastgele hamle', "pyrm", 25],
		['L4E', "pyrl4e", 0],
		['4 tips', "pyr4c", 0],
		['No bar', "pyrnb", 0]
	]],
	['Skewb', [
		["Rastgele Durum (WCA)", "skbso", 0],
		['ideal', "skbo", 0],
		['Rastgele hamle', "skb", 25],
		['No bar', "skbnb", 0]
	]],
	['Kare-1', [
		["Rastgele Durum (WCA)", "sqrs", 0],
		["CSP", "sqrcsp", 0],
		["PLL", "sq1pll", 0],
		['yüz dönüş metriği', "sq1h", 40],
		['büküm metriği', "sq1t", 20]
	]],
	['===DİĞER===', [
		['--', "blank", 0]
	]],
	['15 Bulmaca', [
		['Rastgele Durum URLD', "15prp", 0],
		['Rastgele Durum ^<>v', "15prap", 0],
		['Rastgele Durum Blank', "15prmp", 0],
		['Rastgele hamle URLD', "15p", 80],
		['Rastgele hamle ^<>v', "15pat", 80],
		['Rastgele hamle Blank', "15pm", 80]
	]],
	['8 puzzle', [
		['Rastgele Durum URLD', "8prp", 0],
		['Rastgele Durum ^<>v', "8prap", 0],
		['Rastgele Durum Blank', "8prmp", 0]
	]],
	['LxMxN', [
		['1x3x3 (Floppy küp)', "133", 0],
		['2x2x3 (Tower küp)', "223", 0],
		['2x2x3 (Domino)', "233", 25],
		['3x3x4', "334", 40],
		['3x3x5', "335", 25],
		['3x3x6', "336", 40],
		['3x3x7', "337", 40],
		['8x8x8', "888", 120],
		['9x9x9', "999", 120],
		['10x10x10', "101010", 120],
		['11x11x11', "111111", 120],
		['NxNxN', "cubennn", 12]
	]],
	['Vites Kübü', [
		['Rastgele Durum', "gearso", 0],
		['ideal', "gearo", 0],
		['Rastgele hamle', "gear", 10]
	]],
	['Kilominx', [
		['Rastgele Durum', "klmso", 0],
		['Pochmann', "klmp", 30]
	]],
	['Gigaminx', [
		['Pochmann', "giga", 300]
	]],
	['Crazy Puzzle', [
		['Crazy 3x3x3', "crz3a", 30]
	]],
	['Smetrik', [
		['Smetrik', "cm3", 25],
		['Smetrik mini', "cm2", 25]
	]],
	['Helikopter Kübü', [
		['Heli copter', "heli", 40],
		['Curvy copter', "helicv", 40],
		['2x2 Heli random move', "heli2x2", 70],
		['2x2 Heli by group', "heli2x2g", 5]
	]],
	['Redi Kübü', [
		['Rastgele Durum', "rediso", 0],
		['MoYu', "redim", 8],
		['Rastgele hamle', "redi", 20]
	]],
	['Dino Cube', [
		['Rastgele Durum', "dinoso", 0],
		['ideal', "dinoo", 0]
	]],
	['Sarmaşık Küp', [
		['Rastgele Durum', "ivyso", 0],
		['ideal', "ivyo", 0],
		['Rastgele hamle', "ivy", 10]
	]],
	['Master Pyraminx', [
		['Rastgele Durum', "mpyrso", 0],
		['Rastgele hamle', "mpyr", 42]
	]],
	['Pyraminx Kristal', [
		['Pochmann', "prcp", 70],
		['eski stil', "prco", 70]
	]],
	['Siyam Kübü', [
		['1x1x3 blok', "sia113", 25],
		['1x2x3 blok', "sia123", 25],
		['2x2x2 blok', "sia222", 25]
	]],
	['Square', [
		['Kare-2', "sq2", 20],
		['Süper Kare-1', "ssq1t", 20]
	]],
	['süper disket', [
		[' ', "sfl", 25]
	]],
	['UFO', [
		['Jaap Stili', "ufo", 25]
	]],
	['FTO (Yüz-Döndüren-Octahedron)', [
		['Rastgele Durum', "ftoso", 0],
		['Rastgele hamle', "fto", 30],
		['L3T', "ftol3t", 0],
		['L3T+LBT', "ftol4t", 0],
		['TCP', "ftotcp", 0],
		['edges only', "ftoedge", 0],
		['centers only', "ftocent", 0],
		['corners only', "ftocorn", 0],
		['Diamond Rastgele Durum', "dmdso", 0]
	]],
	['Icosahedron', [
		['Icosamate Rastgele hamle', "ctico", 60]
	]],
	['===ÖZEL===', [
		['--', "blank", 0]
	]],
	['3x3x3 subsetleri', [
		['2-tür hamle R,U', "2gen", 0],
		['2-tür hamle L,U', "2genl", 0],
		['Roux hamleleri M,U', "roux", 0],
		['3-tür hamle F,R,U', "3gen_F", 0],
		['3-tür hamle R,U,L', "3gen_L", 0],
		['3-tür hamle R,r,U', "RrU", 0],
		['Domino Subgroup', "333drud", 0],
		['sadece yarım hamleler', "half", 0],
		['son bölme + son katman (eski)', "lsll", 15]
	]],
	['Bandage küp', [
		['Biküp', "bic", 30],
		['Kare-1 /,(1,0)', "bsq", 25]
	]],
	['ardışık çözümler', [
		['Birsürü 3x3x3\'ler', "r3", 5],
		['234 ardışık', "r234", 0],
		['2345 ardışık', "r2345", 0],
		['23456 ardışık', "r23456", 0],
		['234567 ardışık', "r234567", 0],
		['234 ardışık (WCA)', "r234w", 0],
		['2345 ardışık (WCA)', "r2345w", 0],
		['23456 ardışık (WCA)', "r23456w", 0],
		['234567 ardışık (WCA)', "r234567w", 0],
		['Mini Guildford', "rmngf", 0]
	]],
	['===ESPRİLER===', [
		['--', "blank", 0]
	]],
	['1x1x1', [
		['x y z', "111", 25]
	]],
	['-1x-1x-1', [
		[' ', "-1", 25]
	]],
	['1x1x2', [
		[' ', "112", 25]
	]],
	['LOL', [
		[' ', "lol", 25]
	]],
	['Derrick Eide', [
		[' ', "eide", 25]
	]]
];
var SCRAMBLE_NOOBST = [
	['üst yüzü', 'alt yüzü'],
	['sağ yüzü ', 'sol yüzü'],
	['ön yüzü', 'arka yüzü']
];
var SCRAMBLE_NOOBSS = ' saat yönüne 90 derece çevir,| saatin tersi yönüne 90 derece çevir,| 180 derece çevir,';
var SCROPT_TITLE = 'Karıştırma Seçenekleri';
var SCROPT_BTNALL = 'Tümü';
var SCROPT_BTNNONE = 'Temizle';
var SCROPT_EMPTYALT = 'Lütfen en az bir vaka seçin';
var STATS_CFM_RESET = 'bu sezondaki tüm süreleri sıfırla?';
var STATS_CFM_DELSS = 'Oturumu sil [%s]';
var STATS_CFM_DELMUL = 'buradan silinen değer sayısı?';
var STATS_CFM_DELETE = 'bu süreyi sil?';
var STATS_COMMENT = 'Yorum';
var STATS_REVIEW = 'İnceleme';
var STATS_DATE = 'Tarih';
var STATS_SSSTAT = '1-solve stat.';
var STATS_SSRETRY = 'Retry';
var STATS_CURROUND = 'Şu anki tur istatistikleri';
var STATS_CURSESSION = 'Şu anki sezon istatistikleri';
var STATS_CURSPLIT = 'Mevcut Oturum İstatistiklerinin %d. Aşaması';
var STATS_EXPORTCSV = 'Dışa Aktar (CSV)';
var STATS_SSMGR_TITLE = 'Oturum Yöneticisi';
var STATS_SSMGR_NAME = 'İsim';
var STATS_SSMGR_DETAIL = 'Oturum Ayrıntıları';
var STATS_SSMGR_OPS = 'Yeniden Adlandır|Oluştur|Böl|Birleştir|Sil|Sırala|Merge&Dedupe';
var STATS_SSMGR_ORDER = 'Karıştırmaya göre sırala';
var STATS_SSMGR_ODCFM = 'Tüm oturumları karıştırmaya göre sırala?';
var STATS_SSMGR_SORTCFM = '%d tane çözümler yeniden sıralanacak, onaylıyor musunuz?';
var STATS_ALERTMG = '[%f] oturumundaki tüm zamanları [%t] oturumunun sonuna birleştirelim mi?';
var STATS_PROMPTSPL = 'Oturum [%s]\'den ayrılan en son zamanların sayısı?';
var STATS_ALERTSPL = 'En azından 1 kez ayrılmalı veya bırakmalı';
var STATS_AVG = 'genel ortalama';
var STATS_SUM = 'Toplam';
var STATS_SOLVE = 'çözüm';
var STATS_TIME = 'süre';
var STATS_SESSION = 'sezon';
var STATS_SESSION_NAME = 'Oturum İsmini Düzenle';
var STATS_SESSION_NAMEC = 'Yeni Oturumun Adı';
var STATS_STRING = 'en iyi|şu anki|en kötü|CsTmier tarafından %Y-%M-%D günü oluşturuldu|tamamlanan çözümler/toplam: %d|tek çözüm |%mk çözümün genel ortalaması|%mk çözümün ortalaması|ortalama: %v{ (σ = %sgm)}|genel ortalama: %v|Süreler:|solving from %s to %e|Totally spent: %d|target';
var STATS_PREC = 'Süre dağılım aralığı';
var STATS_PREC_STR = 'otomatik|0.1s|0.2s|0.5s|1s|2s|5s|10s|20s|50s|100s';
var STATS_TYPELEN = 'liste %d türü|liste %d uzunluğu|ortalama|genel ortalama';
var STATS_STATCLR = 'Oturum boşaltmayı etkinleştir';
var STATS_ABSIDX = 'İstatistik raporunda mutlak endeksi göster';
var STATS_XSESSION_DATE = 'herhangi bir zaman|son 24 saat|son 7 gün|son 30 gün|son 365 gün';
var STATS_XSESSION_NAME = 'herhangi bir oturum';
var STATS_XSESSION_SCR = 'herhangi bir karıştırma';
var STATS_XSESSION_CALC = 'Hesapla';
var STATS_RSFORSS = 'Show stat. when clicking solve number';
var PROPERTY_PRINTSCR = 'karıştırmaları istatistiklerde göster';
var PROPERTY_PRINTCOMM = 'istatistiklerdeki yorum(lar)ı yazdır';
var PROPERTY_PRINTDATE = 'istatistiklerde çözülme tarihini yazdır';
var PROPERTY_SUMMARY = 'Süre listesinden önce özeti göster';
var PROPERTY_IMRENAME = 'Sezon oluşturulduğunda yeniden adlandır';
var PROPERTY_SCR2SS = 'karıştırma türü değişince yeni sezon oluştur';
var PROPERTY_SS2SCR = 'sezon değişirken karıştırma türünü koru';
var PROPERTY_SS2PHASES = 'sezon değişirken çok zamanlı kronometreyi koru';
var PROPERTY_STATINV = 'Ters süre listesi';
var PROPERTY_STATSSUM = 'Listede toplam süreyi göster';
var PROPERTY_STATTHRES = 'En iyi oturum için hedef zamanı göster';
var PROPERTY_STATBPA = 'Mümkün olan en iyi ortalamayı (BPA) gösterin';
var PROPERTY_STATWPA = 'En kötü olası ortalamayı göster (WPA)';
var PROPERTY_STATAL = 'İstatistik göstergeleri';
var PROPERTY_STATALU = 'Özelleştirilmiş istatistiksel gösterge';
var PROPERTY_HLPBS = 'PB\'leri vurgula';
var PROPERTY_HLPBS_STR = 'WCA olarak koyu turuncu|Bağlantı rengi olarak|Daha kalın|Hiçbiri';
var PROPERTY_DELMUL = 'Çoklu silmeye izin ver';
var PROPERTY_TOOLSFUNC = 'Seçili Fonksiyonlar';
var PROPERTY_TRIM = 'Çözüm sayısı daha iyi tarafa doğru kırpıldı';
var PROPERTY_TRIMR = 'Çözüm sayısı daha kötü tarafa doğru kırpıldı';
var PROPERTY_TRIM_MED = 'Medyan';
var PROPERTY_STKHEAD = 'Stackmat Durum Bilgilerini kullanın';
var PROPERTY_TOOLPOS = 'Araçlar Paneli Pozisyonu';
var PROPERTY_TOOLPOS_STR = 'Alt|Kayan|Üst';
var PROPERTY_HIDEFULLSOL = 'Çözümü aşamalı olarak göster';
var PROPERTY_IMPPREV = 'Import non-latest data';
var PROPERTY_AUTOEXP = 'Auto Export (per 100 solves)';
var PROPERTY_AUTOEXP_OPT = 'Never|To File|With csTimer ID|With WCA Account|With Google Account|Alert Only';
var PROPERTY_SCRASIZE = 'Otomatik Karıştırma Boyutu';
var MODULE_NAMES = {
	"kernel": 'küresel',
	"ui": 'görünüm',
	"color": 'renk',
	"timer": 'kronometre',
	"scramble": 'karıştırma',
	"stats": 'istatistikler',
	"tools": 'yardımcılar',
	"vrc": 'Sanal&<br>Bluetooth'
};
var BGIMAGE_URL = 'lütfen resim url\'sini girin';
var BGIMAGE_INVALID = 'geçersiz url';
var BGIMAGE_OPACITY = 'arkaplan resmi şeffaflığı';
var BGIMAGE_IMAGE = 'arkaplan resmi';
var BGIMAGE_IMAGE_STR = 'hiçbiri|manual|CCT';
var SHOW_AVG_LABEL = 'ortalama göstergesini göster';
var SHOW_DIFF_LABEL = 'Süre Farkını Göster';
var SHOW_DIFF_LABEL_STR = '-Yeşil +Kırmızı|-Kırmızı +Yeşil|Normal|Hiçbiri';
var USE_LOGOHINT = 'Logodaki ipucu mesajları';
var TOOLS_SCRGEN = 'karıştırma oluşturucu';
var SCRGEN_NSCR = 'karıştırmaların sayısı';
var SCRGEN_PRE = 'karıştırma öncesi';
var SCRGEN_GEN = 'karıştırma oluştur!';
var VRCREPLAY_TITLE = 'Sanal Yeniden Oynatma';
var VRCREPLAY_ORI = 'raw ori|auto ori';
var VRCREPLAY_SHARE = 'Bağlantıyı paylaş';
var GIIKER_CONNECT = 'Bağlanmak için tıklayın';
var GIIKER_RESET = 'Reset (Mark Solved)';
var GIIKER_REQMACMSG = 'Please enter the MAC address of your smart hardware (xx:xx:xx:xx:xx:xx). You can find the MAC address through chrome://bluetooth-internals/#devices, or modify following options to let csTimer automatically obtain it:\nChrome: Turn on chrome://flags/#enable-experimental-web-platform-features\nBluefy: Turn on Enable BLE Advertisements';
var GIIKER_NOBLEMSG = 'Bluetooth API is not available. Ensure https access, check bluetooth is enabled on your device, and try chrome with chrome://flags/#enable-experimental-web-platform-features enabled';
var PROPERTY_SHOWAD = 'Reklamları göster (sayfayı yeniledikten sonra etkili olur)';
var PROPERTY_GIIORI = 'Küp Oryantasyonu';
var LGHINT_INVALID = 'Invalid Value!';
var LGHINT_NETERR = 'Ağ Hatası!';
var LGHINT_SERVERR = 'Sunucu Hatası!';
var LGHINT_SUBMITED = 'Gönderildi';
var LGHINT_SSBEST = 'Session best %s!';
var LGHINT_SCRCOPY = 'Scramble copied';
var LGHINT_LINKCOPY = 'Share link copied';
var LGHINT_SOLVCOPY = 'Solve copied';
var LGHINT_SORT0 = 'Already sorted';
var LGHINT_IMPORTED = 'Import %d session(s)';
var LGHINT_IMPORT0 = 'No session imported';
var LGHINT_BTCONSUC = 'Bluetooth successfully connected';
var LGHINT_BTDISCON = 'Bluetooth disconnected';
var LGHINT_BTNOTSUP = 'Not support your smart cube';
var LGHINT_BTINVMAC = 'Not a valid mac address, cannot connect to your smart cube';
var LGHINT_AEXPABT = 'Auto export abort';
var LGHINT_AEXPSUC = 'Auto export success';
var LGHINT_AEXPFAL = 'Auto export failed';
var EASY_SCRAMBLE_HINT = 'Change length to limit upper bound of solution length, input 2 digits to limit both lower (<= 8) and upper bound';
