@font-face {
	font-family: 'lcd';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'lcd2';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'lcd3';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'lcd4';
	src: url('data:application/x-font-woff;charset=utf-8;base64,d09GRgABAAAAAAjkAA8AAAAAEIgAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAABWAAAABwAAAAcYtGkmk9TLzIAAAF0AAAARwAAAGBvU9QaY21hcAAAAbwAAABZAAABSiFjHAZjdnQgAAACGAAAAAoAAAAKBdIFv2ZwZ20AAAIkAAABsQAAAmVTtC+nZ2FzcAAAA9gAAAAIAAAACAAAABBnbHlmAAAD4AAAAZ0AAATsSma5VGhlYWQAAAWAAAAAMAAAADYF5bK4aGhlYQAABbAAAAAdAAAAJAl2BwlobXR4AAAF0AAAAB8AAAA8Nw4GJWxvY2EAAAXwAAAAIAAAACAG6ghCbWF4cAAABhAAAAAfAAAAIAEuAC5uYW1lAAAGMAAAAl4AAAUi95KxXnBvc3QAAAiQAAAAEwAAACD/JACCcHJlcAAACKQAAABAAAAAQFzJu3kAAAABAAAAAMw9os8AAAAAxQtN8AAAAADRiLPaeJxjYGZ2ZJzAwMrAwirOsouBgZELQjN7M8QyPmJgYGKAgQagJAMScPQJCWZwYNBjsGJL+5fGwMAmw8jOgKpGgYERANRjCKoAeJxjYGBgZoBgGQZGBhBwAfIYwXwWBg0gzQakGRmYGPQYrP7/B/L1GAz+//9/9f8VqHogYGRjgHMYmYAEEwMqYETjA61gYWVj5+Dk4ubh5UOXHJIAAF8iCZ8AAAAAEwUqBSoAlQCVAAB4nF1Ru05bQRDdDQ8DgcTYIDnaFLOZkMZ7oQUJxNWNYmQ7heUIaTdykYtxAR9AgUQN2q8ZoKGkSJsGIRdIfEI+IRIza4iiNDs7s3POmTNLypGqd+lrz1PnJJDC3QbNNv1OSLWzAPek6+uNjLSDB1psZvTKdfv+Cwab0ZQ7agDlPW8pDxlNO4FatKf+0fwKhvv8H/M7GLQ00/TUOgnpIQTmm3FLg+8ZzbrLD/qC1eFiMDCkmKbiLj+mUv63NOdqy7C1kdG8gzMR+ck0QFNrbQSa/tQh1fNxFEuQy6axNpiYsv4kE8GFyXRVU7XM+NrBXbKz6GCDKs2BB9jDVnkMHg4PJhTStyTKLA0R9mKrxAgRkxwKOeXcyf6kQPlIEsa8SUo744a1BsaR18CgNk+z/zybTW1vHcL4WRzBd78ZSzr4yIbaGBFiO2IpgAlEQkZV+YYaz70sBuRS+89AlIDl8Y9/nQi07thEPJe1dQ4xVgh6ftvc8suKu1a5zotCd2+qaqjSKc37Xs6+xwOeHgvDQWPBm8/7/kqB+jwsrjRoDgRDejd6/6K16oirvBc+sifTv7FaAAAAAAEAAf//AA94nI2UP0vDQBjGn7ukCVhE47WNYEWiJmJBl6K9RUTcLo4OLm4Vx+Rb9APILQ76URy66uLQDyBdXS1FEky0jUl7/TMmkDy/573feyBoAxhCQgOaJ81qW0qZvDEgAO2mdIYSVrCGKurYA86JYzvegdWyLc2yLVM7bTmmQ9Jnw9SsVi15JyR54dG7qHiBlME+E/SIx5fkI3SZ4Fww95ZLFpC+F8ZPtE76PH4O6bGoyO9P0gm9aCCY5pCOZH40cBMUUNodsRhYBdxCGu1m/5WKz0uFHuuoYbvY4reA1nJs03Ez/gGPHlP+hxH8lSB9349T2CK/7ibc0Zfvi7d/4nxmGRY2sTPOzMepp7YxFVmYm/46DowvVNPSs2w96Ztmg6UHZSsjA3c6hfZoncf3IZuZMWemOSkKJTMn/oS443G5WDDtRrs5GSY6QunkFnZnOamOz5RUE6isnAaZNWP1Mqg2gPYWaW8qux4u1XbuHs7waonmhaU0FA7sL7JgEmwh1TwjJu4ItEkDQ50l95iZnEdyk2mj24w0pKTXUv4AhhMy3gAAAHicY2BkYGAAYq9FBxfF89t8ZZDnYACBix1b7OG0FwMDcwSrLZDLwcAEEgUAK90JoXicY2BkYGCT+feTgYGDAQSYIxgYGVABPwBGGAKOAAAAeJzjYIAADiBmjGVIYV7E4MW8iOkwhMaNQWoBHaYLZgAAAAAAAAAAAAAMAFIAcACqAOgBGgFWAZoBzAIcAmQCdnicY2BkYGDgZ9BiYGcAASYGRiAWY2BgZIAAAAfUAF4AeJylU8tu00AUPXbSqo0aJFQQFCE0YtFdXNttmjRiEzXqrmyyZeM609aqa5uxk7RdIf6AJX9APwAJIfZ8BD8BazacGU8RUqG8MvLMufee+5h7JwDuO+twUP+e4b3FDm45XYtdNJ2nFjeof25xE6vOG4sXsOZ8tngRbfcq5hIeucriZdx231q8gjX3q8VtDBuvLV5Fq/GFWZzmMqV3JqPGDh469yx2seSMLG5QP7a4iXXnhcULCJ0PFi/igduyeAlP3C2Ll/HYfWXxCkL3o8VtvGzctXgVdxqfsAsFiQgV9wkEDnDOfYwEF9RkOEFOeYiU0hl5GVmSPh61l/xC+Fx941PRV/M66Bm79kp5KkY7wjHtpZEkTx1jZnJqprZVKDDABtfcLI+sq4iaPzP1eIhZ0Smwq2RUyYk4OBfj5EJmJ7kYpvIsyiZSeeJShL7fF+PqPJWdnkdTKlRydFyVQslSqpmceOK4qorBxsZ8PvdKTSzlTGZenDP6yJScsICIBegLCewzccYPo+QoqaK00xP7eUb5u6EW/8r3JvIv3axvwG74nIDudMF+CkraP6BuwL3LXU+nnlLAhcDzQzEslPB7IggHQXfg+2xVEPyskuvZ67z/82Z+Mzf8QRhc88O/PKAbpr9nLigZIzJI4JA+usGFqaA0jYnM456a2B7/Wi361aw6w6nhxrzPj0wdIzXtq6XYxKpojbnXLI8lKCnnkZLiMFeikKrMsygV01J67dYeVazzVKo4qZWiSGXEI86zahpXVDGEbx7CJrb4ELY5zj52qAzCza3udq+/g29Lg/LeAAB4nGNgZgCD/4oMTQxYAAAmBgGmALgB/4WwAY0AS7AIUFixAQGOWbFGBitYIbAQWUuwFFJYIbCAWR2wBitcWACwAyBFsAMrRAGwBCBFsAMrRFmwFCs=') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'lcd5';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'MyImpact';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'iconfont';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'Roboto';
	src: url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff');
	font-weight: normal;
	font-style: normal;
}


* {
	padding: 0;
	margin: 0;
	border: 0;
	box-sizing: border-box;
}

#stats, #stats * {
	box-sizing: content-box;
}

html, body {
	width: 100%;
	height: 100%;
	font-family: Arial;
	overflow: hidden;
}

html.p70 {
	font-size: 9.8px;
	font-size: 1.4vh;
}
html.p80 {
	font-size: 11.2px;
	font-size: 1.6vh;
}
html.p90 {
	font-size: 12.6px;
	font-size: 1.8vh;
}
html {
	font-size: 14px;
	font-size: 2vh;
}
html.p110 {
	font-size: 15.4px;
	font-size: 2.2vh;
}
html.p125 {
	font-size: 17.5px;
	font-size: 2.5vh;
}
html.p150 {
	font-size: 21px;
	font-size: 3vh;
}

input.buttonOK {
	font-size: 1.2em;
	margin: 0 2.5% 0 2.5%;
}

.m input.buttonOK {
	font-size: 1.35em;
}

img {
	display: none;
}

textarea {
	resize: none;
	font-size: 1em;
	overflow-x: hidden;
	overflow-y: auto;
	border-radius: 5px;
	padding: 5px;
}

#timer, #touch {
	position: absolute;
	left: 5%;
	top: 0;
	width: 95%;
	height: 100%;
	z-index: 5;
	border-spacing: 0;
}

#bgImage {
	position: fixed;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
}

#touch {
	z-index: 10;
	-webkit-tap-highlight-color: rgba(0,0,0,0);
}

#container {
	width: 100%;
	text-align: center;
	z-index: 5;
}

#avgstr {
	font-family: Arial;
	position: relative;
}

#container > #avgstr {
	font-size: 0.2em;
}

#astouch {
	position: fixed;
	display: none;
	width: 0;
	height: 0;
	font-family: Arial;
	z-index: 120;
	font-size: 4vmin;
}

#astouch > span {
	position: absolute;
	display: block;
	text-align: center;
	width: 3em;
	height: 3em;
	margin-left: -1.5em;
	margin-top: -1.5em;
	line-height: 3em;
	border-radius: 100%;
	opacity: 0.5;
}

#astouch > span.hit {
	opacity: 1.0;
	box-shadow: 0 0 1em;
	font-weight: bolder;
}

#multiphase > #avgstr {
	font-size: 0.3em;
}

#avgstr > span {
	position: relative;
	display: inline-block;
}

#avgstr > span.click {
	z-index: 20;
}

#multiphase {
	text-align: right;
	z-index: 5;
	padding-right: 0.75rem;
}

#leftbar {
	position: absolute;
	left: 0;
	top: 0;
	border: 2px solid;
	border: 0.15rem solid;
	text-align: center;
	border-radius: 5px;
	border-radius: 0.35rem;
	width: 190px;
	width: 13.5rem;
	height: 190px;
	height: 13.5rem;
}

#leftbar > div {
	position: absolute;
	width: 33.3333%;
	height: 33.3333%;
	cursor: pointer;
	border-radius: 5px;
	border-radius: 0.35rem;
	font-family: Impact;
}

#leftbar > div.c1{
	top: 0;
	left: 0;
}
#leftbar > div.c2{
	top: 0;
	left: 33.3333%;
}
#leftbar > div.c3{
	top: 0;
	right: 0;
}
#leftbar #logo {
	top: 33.3333%;
	left: 0;
}
#leftbar > div.c4{
	bottom: 0;
	left: 0;
}
#leftbar > div.c5{
	bottom: 0;
	left: 33.3333%;
}
#leftbar > div.c6{
	bottom: 0;
	right: 0;
}

#leftbar > div > div {
	width: 100%;
	height: 100%;
	display: table;
	text-align: center;
}

.icon {
	font-family: iconfont;
	font-size: 2em;
}

#leftbar div > div > span {
	vertical-align: middle;
	position: relative;
}

#leftbar div > div > span, html:not(.m) #leftbar div > div:hover > span.icon {
	display: none;
}

html:not(.m) #leftbar div > div:hover > span, #leftbar div > div > span.icon, #leftbar > #logo > div > span {
	display: table-cell;
}

#leftbar #logo {
	font-size: 3.2em;
	font-family: MyImpact;
	width: 100%;
	border-width: 2px;
	border-width: 0.15rem;
	overflow: hidden;
	white-space: nowrap;
}

#leftbar #logo > div > span > div.pad {
	display: inline-block;
	top: 0;
	bottom: 0;
}

#leftbar #logo > div > span > span.msg {
	font-family: sans-serif;
	margin: 0 1em 0 1em;
	line-height: 100%;
}

#leftbar #logo > div > span.hint {
	-webkit-animation: flyLeft 3s 1 ease-in-out;
	-moz-animation: flyLeft 3s 1 ease-in-out;
	animation: flyLeft 3s 1 ease-in-out;
}

@keyframes flyLeft {
	to {transform: translateX(-100%);}
}

@-webkit-keyframes flyLeft {
	to {transform: translateX(-100%);}
}

@-moz-keyframes flyLeft {
	to {transform: translateX(-100%);}
}

.mybutton {
	border-style: outset;
	border-width: 1px;
	border-width: 0.1rem;
}

.mybutton.enable, .tab.enable {
	border-style: inset;
}

#wndctn {
	z-index: 30;
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	left: env(safe-area-inset-left);
	top: env(safe-area-inset-top);
	right: env(safe-area-inset-right);
	bottom: env(safe-area-inset-bottom);
	visibility: hidden;
}

#wndctn > div {
	visibility: visible;
}

.mywindow {
	position: absolute;
	padding: 4px;
	padding: 0.3rem;
	border-radius: 5px;
	border-radius: 0.35rem;
	border: 2px solid;
	border: 0.15rem solid;
	z-index: auto;
}

.mywindow:not(.fixed)::before, #leftbar::before {
	content: "";
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	border-radius: 0.35rem;
	z-index: -10;
	margin: -2px;
	margin: -0.15rem;
}

html:not(.m) .mywindow:hover, html:not(.m) #leftbar:hover {
	z-index: 35;
}

.mywindow.highlight {
	border: 2px solid;
	border: 0.15rem solid;
}

.mywindow.fixed {
	background-color: transparent;
	border-color: transparent;
	box-shadow: none;
	padding: 0;
}

.mywindow > .chide {
	cursor: pointer;
	position: absolute;
	width: 1rem;
	height: 1rem;
	border-radius: 0;
	border-bottom-right-radius: 1rem;
	top: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.1);
}

.m .mywindow > .chide {
	width: 2rem;
	height: 2rem;
	border-bottom-right-radius: 2rem;
}

#gray {
	z-index: 50;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: none;
}

.dialog, .popup {
	position: absolute;
	z-index: 100;
	padding: 8px;
	padding: 0.55rem;
	border-radius: 5px;
	border-radius: 0.35rem;
	border: 2px outset;
	border: 0.15rem outset;
	text-align: center;
	border-spacing: 0;
	display: none;
}

.dialog > table {
	table-layout: fixed;
	width: 100%;
	height: 100%;
}

.dialog .value {
	overflow-y: auto;
}

.dialog .value, .dialog .value > * {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}

.dialog .title {
	font: 1.5em bold;
	font-family: Impact;
	white-space: break-spaces;
	overflow-x: hidden;
}

.dialog .button {
	bottom: 5px;
	bottom: 0.35rem;
	left: 8px;
	left: 0.55rem;
	right: 8px;
	right: 0.55rem;
}

.options {
	height: 100%;
	line-height: 150%;
	border-spacing: 0;
}

.options > tbody {
	height: 100%;
}

.options th:first-child {
	padding: 0.6em;
	font-size: 1.2em;
	text-transform: capitalize;
	border-radius: 0 1.2em 1.2em 0;
}

.options .tab > .icon, .m .options .tab > span {
	display: none;
}

.m .options .tab > .icon {
	display: initial;
}

.m .dialog {
	font-size: 1.2em;
}

.tab {
	border: 1px outset;
	border: 0.1rem outset;
	text-align: center;
	margin: 5px;
	margin: 0.35rem;
	margin-right: 0;
	border-radius: 5px;
	border-radius: 0.35rem;
	font-family: Impact;
	width: auto;
	padding: 0 5px;
	padding: 0 0.35rem;
	white-space: nowrap;
}

.tabValue {
	width: 100%;
	height: 100%;
	text-align: left;
	overflow: hidden;
	border: 1px solid;
	border: 0.1rem solid;
	border-radius: 5px;
	border-radius: 0.35rem;
}

.tabValue > * {
	display: block;
	height: 100%;
	overflow-y: auto;
	overflow-x: hidden;
}

.noScrollBar {
	padding-right: 21px;
	padding-right: 1.5rem;
	margin-right: -21px;
	margin-right: -1.5rem;
	scrollbar-width: none;
}

.noScrollBar::-webkit-scrollbar {
	display: none;
}

.click, .times, .tab {
	cursor: pointer;
	border-radius: 0.15em;
}

.times.pb {
	font-weight: bold;
}

a {
	color: inherit;
}

.click, .times, .tab, .table, .mybutton, .tabValue, .expOauth, .title, #touch, #lcd, .noselect {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

#lcd {
	white-space: nowrap;
}

#lcd::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 10;
}

.insplabel {
	font-family: iconfont;
	position: absolute;
	font-size: 0.2em;
	opacity: 0.65;
	display: none;
}

.insp.activetimer {
	position: relative;
}

.insp.activetimer > .insplabel {
	display: inline-block;
}

#rtimer .insplabel {
	top: -1.0em;
	right: 0;
}

#timer .difflabel {
	display: inline-block;
	font-family: Arial;
	font-size: 0.15em;
}

.m #timer .difflabel {
	display: block;
	font-size: 0.2em;
	margin-top: -0.4em;
}

#rtimer .difflabel {
	margin-top: -0.4em;
	font-family: Arial;
	font-size: 0.25em;
}

.playbutton.click {
	display: inline-block;
	min-width: 7%;
	font-family: iconfont, Arial;
}

#about {
	line-height: 1.6em;
	text-align: left;
}

#about .tab {
	white-space: break-spaces;
	min-width: min(15vw, 6em);
	max-height: 3.5em;
	overflow: visible;
}

#stats {
	left: 0;
	top: 190px;
	top: 13.5rem;
	bottom: 0;
	text-align: center;
}

#stats table {
	min-width: 178px;
	min-width: 12.6rem;
	white-space: nowrap;
}

.stattl {
	margin-top: 2px;
	margin-top: 0.15rem;
	overflow-x: hidden;
}

.myscroll {
	overflow-y: auto;
	text-align: center;
	padding-right: 21px;
	padding-right: 1.5rem;
	margin-right: -21px;
	margin-right: -1.5rem;
	width: 100%;
}

.myscroll::-webkit-scrollbar {
	display: none;
}

.statc {
	display: inline-block;
	width: 100%;
}

table.sumtable {
	border-style: dashed;
	width: 100%;
}

table.sumtable td, table.sumtable th {
	border-style: none;
}

table.opttable {
	border-collapse: collapse;
	width: 100%;
	margin: 0.1em;
}

table.opttable th .icon {
	font-weight: normal;
	font-size: 1em;
}

table.opttable td {
	padding: 0.2em 0.1em 0.2em 0.3em;
}

table.opttable .sr {
	width: 0;
	padding-left: 0.5em;
	white-space: nowrap;
	text-align: right;
	font-weight: normal;
}

.opttable td.sr {
	border-left: 1px dashed;
	border-left: 0.1rem dashed;
}

div.helptable > * {
	padding: 0.2em;
}

div.helptable ul {
	padding-left: 1.2em;
}

.table {
	border-collapse: collapse;
	text-align: center;
}

.table td, .table th, .table {
	border: 1px solid;
	border: 0.1rem solid;
	padding: 4px;
	padding: 0.3rem;
}

.fixed .table td, .fixed .table th, .fixed .table {
	padding: 2px;
	padding: 0.15rem;
}

.ssmgr {
	width: 100%;
}

.cntbar {
	display: inline-block;
	text-align: center;
	border: 1px solid;
	border: 0.1rem solid;
}

.cntbar.sty2 {
	filter: invert(1) brightness(2);
}

#inputTimer {
	font-size: 0.5em;
	text-align: center;
	width: 60%;
}

.m #inputTimer {
	position: relative;
	right: 10%;
}

#scrambleTxt {
	width: 100%;
	white-space: pre-wrap;
	overflow-x: hidden;
	overflow-y: auto;
	max-height: 600px;
	max-height: 42rem;
	max-height: 80vh;
}

#scrambleTxt .smrtScrDim {
	filter: opacity(50%) blur(1px);
}

#scrambleTxt .smrtScrMrk {
	color: #0d0;
	font-size: 75%;
}

#scrambleTxt .smrtScrDim,
#scrambleTxt .smrtScrCur,
#scrambleTxt .smrtScrAct,
#scrambleTxt .smrtScrMrk {
	border-radius: 15% / 30%;
	padding-left: 0.3em;
	padding-right: 0.3em;
	display: inline-block;
}

#scrambleTxt.limit {
	max-height: 150px;
	max-height: 10.5rem;
}

#scrambleDiv {
	right: 0;
	left: 190px;
	left: 13.5rem;
	top: 0;
}

#scrambleDiv > .title {
	margin-bottom: 0.5em;
}

#toolsDiv > div > div {
	font-family: Lucida Console, Monospace;
	font-size: 1.5em;
	text-align: left;
	max-height: 30vh;
	overflow-x: hidden;
	overflow-y: auto;
	padding-right: 2em;
	margin-right: -2em;
}

#toolsDiv > div {
	display: inline-block;
	overflow-x: hidden;
	margin: 3px;
	margin: 0.2rem;
}

#toolsDiv {
	right: 0;
	bottom: 0;
	text-align: right;
}

.sol {
	white-space: nowrap;
}

.dialoglogo {
	left: 15%;
	right: 15%;
	top: 15%;
	bottom: 15%;
}

.dialogoption, .dialogshare {
	left: 25%;
	right: 25%;
	top: 20%;
	bottom: 20%;
}

.dialogcfm {
	left: 30%;
	right: 30%;
	top: 30%;
	bottom: 30%;
}

.dialoginput, .dialogstats, .dialogexport, .dialogscropt, .dialogdonate {
	left: 20%;
	right: 20%;
	top: 20%;
	bottom: 20%;
}

.dialogcasestats {
	left: 30%;
	right: 30%;
	top: 15%;
	bottom: 15%;
}

.dialogcasestats .table {
	width: 100%;
	font-size: 1.5em;
}

.dialogssmgr {
	left: 15%;
	right: 15%;
	top: 20%;
	bottom: 20%;
}

.dialogexport {
	top: 15%;
	bottom: 15%;
}

.sflt > div {
	white-space: nowrap;
}

.sflt > div > div {
	white-space: normal;
}

.dialogscropt .sflt label, .dialogscropt .sflt > div > div.sgrp > span:first-child {
	display: inline-block;
	min-width: 45%;
	text-align: left;
	padding: 0.2em 0.2em 0.2em 5%;
}

.dialogscropt .sflt label {
	min-width: 24%;
}

.dialogscropt .bimg {
	border: 1px dashed;
	border-radius: 3px;
	border-radius: 0.2rem;
}

select, input[type='button'], input[type='text'] {
	cursor: pointer;
	border: 1px solid;
	border: 0.1rem solid;
	border-radius: 3px;
	border-radius: 0.2rem;
	padding: 1px;
	padding: 0.1rem;
	vertical-align: middle;
	font-size: 1em;
	-webkit-appearance: none;
	-moz-appearance:    none;
	appearance:         none;
}

select {
	max-width: 100px;
	max-width: 7em;
}

#toolsDiv > div > span > select.twolv1, #toolsDiv > div > span > select.twolv2 {
	max-width: 60px;
	max-width: 5em;
}

td.seltd {
	padding: 0;
	width: 0;
}

td.seltd > select {
	width: 100%;
	min-width: 3em;
}

input:disabled {
	cursor: default;
	border: none;
}

input[type='button'] {
	padding: 1px 5px 1px 5px;
	padding: 0.1rem 0.35rem 0.1rem 0.35rem;
}

input[type="text"] {
	width: 35px;
	width: 2.5rem;
	text-align: center;
}

input[type="file"] {
	width: 100%;
}

input[type="color"] {
	vertical-align: middle;
}

input[type="color"].mulcolor {
	width: 1.5em;
}

.touchcube {
	display: table;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	margin: auto;
	user-select: none;
	font-family: Arial;
	border-collapse: collapse;
	color: transparent;
}

.touchcube tr td {
	width: 33%;
	height: 33%;
}

.touchcube.active {
	background-color: #6666;
	color: #fffa;
}

.touchcube.active td.touchto {
	background-color: #0f0a;
}

.touchcube.active td.touchfrom {
	background-color: #f00a;
}

.touchcube.board td {
	border: 2px solid #6666;
	border: 0.15rem solid #6666;
}

.expOauth {
	width: 80%;
	margin-left: 10%;
	margin-right: 10%;
	font-size: 1.2em;
	cursor: pointer;
	border-collapse: separate;
	text-align: center;
	border: 1px dashed;
	border-radius: 5px;
	margin-bottom: 5px;
}

.expOauth table {
	width: 100%;
}

.expOauth .img {
	display: block;
	height: 2.5em;
	width: 2.5em;
}

#wcaLogin .img {
	background: url('data:image/svg+xml;base64,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') center/2.5em 2.5em no-repeat;
}

#gglLogin .img {
	background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgNDggNDgiPjxkZWZzPjxwYXRoIGlkPSJhIiBkPSJNNDQuNSAyMEgyNHY4LjVoMTEuOEMzNC43IDMzLjkgMzAuMSAzNyAyNCAzN2MtNy4yIDAtMTMtNS44LTEzLTEzczUuOC0xMyAxMy0xM2MzLjEgMCA1LjkgMS4xIDguMSAyLjlsNi40LTYuNEMzNC42IDQuMSAyOS42IDIgMjQgMiAxMS44IDIgMiAxMS44IDIgMjRzOS44IDIyIDIyIDIyYzExIDAgMjEtOCAyMS0yMiAwLTEuMy0uMi0yLjctLjUtNHoiLz48L2RlZnM+PGNsaXBQYXRoIGlkPSJiIj48dXNlIHhsaW5rOmhyZWY9IiNhIiBvdmVyZmxvdz0idmlzaWJsZSIvPjwvY2xpcFBhdGg+PHBhdGggY2xpcC1wYXRoPSJ1cmwoI2IpIiBmaWxsPSIjRkJCQzA1IiBkPSJNMCAzN1YxMWwxNyAxM3oiLz48cGF0aCBjbGlwLXBhdGg9InVybCgjYikiIGZpbGw9IiNFQTQzMzUiIGQ9Ik0wIDExbDE3IDEzIDctNi4xTDQ4IDE0VjBIMHoiLz48cGF0aCBjbGlwLXBhdGg9InVybCgjYikiIGZpbGw9IiMzNEE4NTMiIGQ9Ik0wIDM3bDMwLTIzIDcuOSAxTDQ4IDB2NDhIMHoiLz48cGF0aCBjbGlwLXBhdGg9InVybCgjYikiIGZpbGw9IiM0Mjg1RjQiIGQ9Ik00OCA0OEwxNyAyNGwtNC0zIDM1LTEweiIvPjwvc3ZnPgo=') center/2.5em 2.5em no-repeat;
}

.expUpDown td {
	border: 1px solid;
	border-radius: 5px;
	width: 50%;
	height: 2.5em;
	padding: 0.5em;
}

html.m.p70 {
	font-size: 1.05vw;
	font-size: calc(0.45vw + 0.80vh);
}

html.m.p80 {
	font-size: 1.20vw;
	font-size: calc(0.52vw + 0.92vh);
}

html.m.p90 {
	font-size: 1.35vw;
	font-size: calc(0.58vw + 1.03vh);
}

html.m {
	font-size: 1.50vw;
	font-size: calc(0.65vw + 1.15vh);
}

html.m.p110 {
	font-size: 1.65vw;
	font-size: calc(0.71vw + 1.26vh);
}

html.m.p125 {
	font-size: 1.87vw;
	font-size: calc(0.81vw + 1.43vh);
}

html.m.p150 {
	font-size: 2.25vw;
	font-size: calc(0.97vw + 1.72vh);
}

html.m .mhide, html:not(.m) .mshow {
	display: none;
}

.mshow.t td, .mshow.t th {
	border-bottom-style: dashed;
}

.mshow.b td, .mshow.b th {
	border-top-style: none;
}

.m #timer, .m #touch {
	font-size: 0.6em;
	left: 0;
	width: 100%;
}

.m #leftbar {
	left: 0;
	bottom: 0;
	top: auto;
	right: 0;
	width: auto;
	height: 59px;
	height: 11.8vw;
	font-size: 2.5vw;
}

.m #leftbar > div {
	top: 0;
	width: 11.1111%;
	height: 100%;
}

.m #leftbar > div.c1{
	left: 0;
}
.m #leftbar > div.c2{
	left: 11.1111%;
}
.m #leftbar > div.c3{
	left: 22.2222%;
}
.m #leftbar #logo {
	left: 33.3333%;
}
.m #leftbar > div.c4{
	left: 66.6667%;
}
.m #leftbar > div.c5{
	left: 77.7778%;
}
.m #leftbar > div.c6{
	left: 88.8889%;
}

.m #leftbar #logo {
	width: 33.3333%;
	top: 0;
}

.m .mybutton {
	font-size: 1.2em;
}

.m #stats {
	top: auto;
	max-width: 100%;
	bottom: 61px;
	bottom: 11.8vw;
	white-space: nowrap;
	box-sizing: border-box;
}

.m #stats table {
	min-width: 0;
	white-space: nowrap;
}

.m #scrambleDiv {
	left: 0;
	width: 100%;
}

.m .statc {
	margin-right: 5px;
	margin-right: 0.35rem;
	margin-top: 2px;
	margin-top: 0.15rem;
	width: auto;
	vertical-align: top;
}

.m .stattl {
	display: inline-block;
	width: auto;
}

.m #toolsDiv {
	bottom: 61px;
	bottom: 11.8vw;
}

.m #toolsDiv > div > div {
	font-size: 1.2em;
}

.m .dialogoption, .m .dialogshare, .m .dialoginput, .m .dialogstats, .m .dialogcasestats, .m .dialogssmgr, .m .dialogscropt, .m .dialogexport, .m .dialoglogo, .m .dialogdonate {
	left: 5%;
	right: 5%;
}

.m .dialogcfm {
	left: 15%;
	right: 15%;
	top: 25%;
	bottom: 25%;
}

.m .dialogexport {
	top: 15%;
	bottom: 15%;
}

.m #lcd {
	max-width: 480px;
	max-width: 100vw;
	overflow: hidden;
}

.m.toolf .mywindow, .m.toolt .mywindow {
	position: relative;
}

.m.toolf #wndctn {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap-reverse;
	align-content: end;
}

.m.toolf #toolsDiv {
	margin-left: auto;
}

.m.toolt #toolsDiv {
	margin-left: auto;
	bottom: 0;
}

.m.toolf #scrambleDiv, .m.toolt #stats {
	position: absolute;
}

.m.toolt #wndctn {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-content: start;
}

#vrckey {
	table-layout: fixed;
	border-collapse:collapse;
	font-family: monospace;
}

#vrckey tr {
	height: 1.5em;
}

#vrckey td {
	line-height: 1em;
	text-align: left;
}

#vrckey span {
	display: block;
	width: 1.5em;
	text-align: right;
	font-weight: 900;
}

.mtds textarea,
.mtds #leftbar,
.mtds #leftbar > div,
.mtds .mywindow,
.mtds .mywindow:not(.fixed)::before,
.mtds #leftbar::before,
.mtds .dialog,
.mtds .popup,
.mtds .tab,
.mtds .tabValue,
.mtds .click:not(td):not(th),
.mtds .tab,
.mtds select,
.mtds input[type='button'],
.mtds input[type='text'] {
	border-radius: unset;
	border-color: transparent;
}

.mtds .tab {
	position: relative;
}

.mtds .tab:not(.enable)::before {
	content: "";
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: -10;
	margin: 2px;
	margin: -0.1rem;
	box-shadow: 0 0 0.2em;
}

.mtds .mywindow > .chide {
	top: -0.15rem;
	left: -0.15rem;
}

.instruction {
	margin: 2em;
	text-align: left;
}

.instruction ul {
	margin: 1em;
	padding-left: 1.5em;
	list-style: disc;
}

.colorPrevV, .m .colorPrevH {
	display: None;
}

.m .colorPrevV, .colorPrevH {
	display: inline-block;
	text-align: center;
	border: #0008 dashed;
	margin: 0.2em;
}

/* cstimer plus */
.cspt .mywindow, .cspt #leftbar > div, .cspt #avgstr {
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji" !important;
}

.cspt #avgstr {
    font-size: 2rem;
    line-height: 1.6;
}

.cspt .mywindow {
    padding: 1rem;
    border: 0;
    border-radius: 0;
}

.cspt #scrambleTxt {
    cursor: default;
    letter-spacing: 0.375rem;
    padding: 7px;
}

.cspt:not(.m) #leftbar {
    width: 17.2rem;
}

.cspt:not(.m) #scrambleDiv {
    left: 17.2rem;
}

.cspt #leftbar, .cspt #leftbar > div {
    border: 0;
    border-radius: 0;
}

.cspt .mywindow:not(.fixed)::before, .cspt #leftbar::before {
    margin: 0px !important;
}

.cspt #leftbar::before, .cspt .mywindow::before, .cspt .popup, .cspt .dialog {
    box-shadow: 0 1px 8px rgba(0,0,0,0.2) !important;
    border-radius: 0;
}

.cspt .mywindow > .chide {
    background-color: rgba(0,0,0,.2);
}

.cspt select, .cspt input[type='button'], .cspt input[type='text'] {
    background-color: rgba(255,255,255,0.1);
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    border: 0;
    margin: 0 0.25rem;
}

.cspt select > option {
    color: #000000;
}

.cspt input[type='button'] {
    padding: 0.375rem 0.75rem;
}

.cspt .table td, .cspt .table th, .cspt .table {
    padding: 0.5rem;
}

.cspt .statc {
    margin-top: 1rem;
}

.cspt .stattl {
    margin-top: 0.5rem;
}

.cspt #leftbar .mybutton .icon {
    height: 100%;
    font-size: 2rem;

    display: flex;
    justify-content: center;
    align-items: center;

    speak: none;
    font-style: normal;
    font-weight: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    font-variant: normal;
}

@-moz-document url-prefix() {
    .cspt #leftbar .mybutton .icon {
        display: table-cell;
        vertical-align: middle;
    }
}

.cspt #leftbar .mybutton .icon:before {
    font-size: 2rem;
}

.cspt #gray {
    background-color: #000;
}

.cspt .dialog, .cspt .popup {
    border-radius: 0;
    border: 0;
}

.cspt .dialog .title {
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji" !important;
}

.cspt .tab {
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji" !important;
    border: 0;
    padding: 0.375rem;
    border-radius: 0;
}

.cspt #inputTimer {
    width: 70% !important;
}

.cspt .tab.enable {
    background-color: rgba(255,255,255,0.2)
}

.cspt table.opttable td {
    padding: 0.75rem;
}

html.cspt:not(.m) .mywindow, html.cspt:not(.m) #leftbar {
    z-index: 1 !important;
}

html.cspt:not(.m) .mywindow:hover, html.cspt:not(.m) #leftbar:hover {
    z-index: 1 !important;
}
