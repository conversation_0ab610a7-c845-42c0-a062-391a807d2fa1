var OK_LANG = 'Đồng Ý';
var CANCEL_LANG = 'Hủy';
var RESET_LANG = 'Thiết lập lại';
var ABOUT_LANG = 'Thông tin';
var ZOOM_LANG = 'Phóng to';
var COPY_LANG = 'Copy';
var BUTTON_TIME_LIST = 'THÀNH<br>TÍCH';
var BUTTON_OPTIONS = 'Cài đặt';
var BUTTON_EXPORT = 'Giải phóng';
var BUTTON_DONATE = 'ỦNG HỘ';
var PROPERTY_SR = 'With session';
var PROPERTY_USEINS = 'dử dụng thời gian chuẩn bị của WCA';
var PROPERTY_USEINS_STR = 'Always (down)|Always (up)|Except BLD (down)|Except BLD (up)|Never';
var PROPERTY_SHOWINS = 'Hiện một hình ảnh khi thời gian quan sát được bật';
var PROPERTY_VOICEINS = 'Giọng nói cho thời gian chuẩn bị của WCA';
var PROPERTY_VOICEINS_STR = 'Vô giới tính :)) |Nam giới|Nữ giới';
var PROPERTY_VOICEVOL = 'Voice volume';
var PROPERTY_PHASES = 'nhiều đợt';
var PROPERTY_TIMERSIZE = 'kích cớ của timer';
var PROPERTY_USEMILLI = 'sử dụng 0.001s';
var PROPERTY_SMALLADP = 'Sử dụng font nhỏ hơn cho chữ số hàng thập phân';
var PROPERTY_SCRSIZE = 'kích cớ của scramble';
var PROPERTY_SCRMONO = 'Khoảng cách của các chữ scramble';
var PROPERTY_SCRLIM = 'Giới hạn chiều cao của vùng scramble';
var PROPERTY_SCRALIGN = 'Vị trí của scramble';
var PROPERTY_SCRALIGN_STR = 'Giữa|Trái|Phải';
var PROPERTY_SCRWRAP = 'Scramble Wrap';
var PROPERTY_SCRWRAP_STR = 'Balanced|Normal';
var PROPERTY_SCRNEUT = 'Color neutral';
var PROPERTY_SCRNEUT_STR = 'None|Single face|Double faces|Six faces';
var PROPERTY_SCREQPR = 'Probabilities for training-scramble states';
var PROPERTY_SCREQPR_STR = 'Actual|Equal|Random order';
var PROPERTY_SCRFAST = 'Dùng công thức tráo nhanh cho 4x4x4 (không chính thức)';
var PROPERTY_SCRKEYM = 'Đánh dấu (các) bước xoay quan trọng trong công thức tráo';
var PROPERTY_SCRCLK = 'Hành động khi bấm vào một cách tráo';
var PROPERTY_SCRCLK_STR = 'Không|Sao Chép|Xáo tiếp theo';
var PROPERTY_WNDSCR = 'Cách hiển thị thanh công thức tráo';
var PROPERTY_WNDSTAT = 'Cách hiển thị thanh thống kê thời gian';
var PROPERTY_WNDTOOL = 'Cách hiển thị bảng công cụ';
var PROPERTY_WND_STR = 'Bình thường|Chìm';
var EXPORT_DATAEXPORT = 'Thu thập/giải phóng data';
var EXPORT_TOFILE = 'giải phóng thành file';
var EXPORT_FROMFILE = 'Thu thập từ file';
var EXPORT_TOSERV = 'Giải phóng ra server';
var EXPORT_FROMSERV = 'Thu thập từ  server';
var EXPORT_FROMOTHER = 'Nhập (các) phiên từ các công cụ tính thời gian khác';
var EXPORT_USERID = 'Hãy viết mã riêng đặc biệt của bạn (chỉ viết bằng chữ cái và số)';
var EXPORT_INVID = 'Chỉ được viết chữ cái hoặc con số!';
var EXPORT_ERROR = 'Có lỗi xảy ra...';
var EXPORT_NODATA = 'không tìm thấy thông tin từ mã của bạn';
var EXPORT_UPLOADED = 'thu thập thành công';
var EXPORT_CODEPROMPT = 'Lưu code này,hoắc ghi code đã lưu để nhập vào';
var EXPORT_ONLYOPT = 'Export/Import only Options';
var EXPORT_ACCOUNT = 'Xuất Tài Khoản';
var EXPORT_LOGINGGL = 'Đăng Nhập Bằng Tài Khoản Google';
var EXPORT_LOGINWCA = 'Đăng Nhập Bằng Tài Khoản WCA';
var EXPORT_LOGOUTCFM = 'Xác nhận đăng xuất?';
var EXPORT_LOGINAUTHED = 'Đã Xác Thực<br>Đang Lấy Dữ Liệu...';
var EXPORT_AEXPALERT = 'More than %d solves since last backup';
var EXPORT_WHICH = 'You have %d file(s), which one should be imported?';
var EXPORT_WHICH_ITEM = '%s solve(s), uploaded at %t';
var IMPORT_FINAL_CONFIRM = 'This will override all local data! It will modify %d sessions, add %a and remove %r solves at least. Confirm to import data?';
var BUTTON_SCRAMBLE = 'Xáo<br>trộn';
var BUTTON_TOOLS = 'Công cụ';
var IMAGE_UNAVAILABLE = 'Không khả dụng cho loại scramble này';
var TOOLS_SELECTFUNC = 'Chức năng';
var TOOLS_CROSS = 'giải cross';
var TOOLS_EOLINE = 'giải EOLine';
var TOOLS_ROUX1 = 'giải Roux S1';
var TOOLS_222FACE = '2x2x2 face';
var TOOLS_GIIKER = 'Rubik có kết nối Bluetooth';
var TOOLS_IMAGE = 'vẽ scramble';
var TOOLS_STATS = 'Thành tích ( kiểu 1)';
var TOOLS_HUGESTATS = 'bắt đầu dấu cộng';
var TOOLS_DISTRIBUTION = 'Thành tích ( kiểu 2)';
var TOOLS_TREND = 'Thành tích ( kiểu 3)';
var TOOLS_METRONOME = 'Đếm nhịp';
var TOOLS_RECONS = 'Tái tạo';
var TOOLS_RECONS_NODATA = 'Không tìm thấy cách giải.';
var TOOLS_RECONS_TITLE = 'insp|exec|turn|tps';
var TOOLS_TRAINSTAT = 'Thông số luyện tập';
var TOOLS_BLDHELPER = 'Hỗ trợ giải bịt mắt';
var TOOLS_CFMTIME = 'Xác nhận';
var TOOLS_SOLVERS = 'Công cụ giải';
var TOOLS_DLYSTAT = 'Daily Statistics';
var TOOLS_DLYSTAT1 = 'Period|Start of Day|Week';
var TOOLS_DLYSTAT_OPT1 = 'day|week|month|year';
var TOOLS_DLYSTAT_OPT2 = 'Sun|Mon|Tue|Wed|Thu|Fri|Sat';
var TOOLS_SYNCSEED = 'Common Scramble';
var TOOLS_SYNCSEED_SEED = 'Hạt giống ';
var TOOLS_SYNCSEED_INPUT = 'Nhập Hạt Giống';
var TOOLS_SYNCSEED_30S = 'Use 30s Seed';
var TOOLS_SYNCSEED_HELP = 'If enabled, scramble will only depend on the seed and scramble settings.';
var TOOLS_SYNCSEED_DISABLE = 'Disable current seed?';
var TOOLS_SYNCSEED_INPUTA = 'Input a value (a-zA-Z0-9) as seed';
var TOOLS_BATTLE = 'Online battle';
var TOOLS_BATTLE_HEAD = 'Room|Join Room';
var TOOLS_BATTLE_TITLE = 'Rank|Status|Time';
var TOOLS_BATTLE_STATUS = 'Ready|Inspect|Solving|Solved|Lost';
var TOOLS_BATTLE_INFO = 'Join a battle room with your friend, then you will battle together.';
var TOOLS_BATTLE_JOINALERT = 'Please input the room ID';
var TOOLS_BATTLE_LEAVEALERT = 'Leave current room';
var OLCOMP_UPDATELIST = 'Update Competition List';
var OLCOMP_VIEWRESULT = 'View Result';
var OLCOMP_VIEWMYRESULT = 'My History';
var OLCOMP_START = 'Bắt đầu!';
var OLCOMP_SUBMIT = 'Gửi!';
var OLCOMP_SUBMITAS = 'Gửi Như:';
var OLCOMP_WCANOTICE = 'Gửi Bằng Tài Khoản WCA? (Đăng nhập lại nếu không được nhận sau khi gửi) ';
var OLCOMP_OLCOMP = 'Cuộc Thi Trực Tuyến';
var OLCOMP_ANONYM = 'Anonym';
var OLCOMP_ME = 'Me';
var OLCOMP_WCAACCOUNT = 'Tài Khoản WCA';
var OLCOMP_ABORT = 'Abort competition and show results?';
var OLCOMP_WITHANONYM = 'Với tên giả';
var PROPERTY_IMGSIZE = 'kích cớ hình vẽ scramble';
var PROPERTY_IMGREP = 'Show virtual cube animation when clicking scramble image';
var TIMER_INSPECT = 'Chuẩn bị';
var TIMER_SOLVE = 'GIải';
var PROPERTY_USEMOUSE = 'sử dụng chuột';
var PROPERTY_TIMEU = 'cập nhập thời gian';
var PROPERTY_TIMEU_STR = 'Cập nhập|theo 0.1s|theo giây|Chuẩn bị|Không có';
var PROPERTY_PRETIME = 'Thời gian giữ (Giây(s))';
var PROPERTY_ENTERING = 'Nhập thời gian với';
var PROPERTY_ENTERING_STR = 'timer|gõ vào|stackmat|MoYuTimer|Giả lập|Bluetooth|qCube|GanTimer|last layer training';
var PROPERTY_INTUNIT = 'Unit when entering an integer';
var PROPERTY_INTUNIT_STR = 'second|centisecond|millisecond';
var PROPERTY_COLOR = 'Chọn màu nền';
var PROPERTY_COLORS = 'màu chữ|Màu nền|Màu bảng|Màu nút|Màu link|Màu chữ logo|màu nền logo';
var PROPERTY_VIEW = 'Kiểu giao diện người dùng';
var PROPERTY_VIEW_STR = 'Tự động|Điện thoại|Máy tính';
var PROPERTY_UIDESIGN = 'UI design is';
var PROPERTY_UIDESIGN_STR = 'Normal|Material design|Normal w/o shadows|Material design w/o shadows';
var COLOR_EXPORT = 'Vui lòng lưu lại chuỗi này để có thể nhập lại lần sau';
var COLOR_IMPORT = 'Vui lòng gõ vào chuỗi bạn đã dùng để xuất dữ liệu';
var COLOR_FAIL = 'Dữ liệu sai, không thể nhập được';
var PROPERTY_FONTCOLOR_STR = 'Đen|Trắng';
var PROPERTY_COLOR_STR = 'tự chọn|import/export...|bất kì|kiểu 1|kiểu 2|Kiểu 3|đen|trắng|Kiểu6|solarized dark|solarized light';
var PROPERTY_FONT = 'Kiểu chữ timer';
var PROPERTY_FONT_STR = 'bất kì|bình thường|kí thuật số 1|kí thuật số 2|kí thuật số 3|kí thuật số 4|kí thuật số 5';
var PROPERTY_FORMAT = 'Thể loại thời gian';
var PROPERTY_USEKSC = 'Sử dụng nút tắt';
var PROPERTY_USEGES = 'use gesture control';
var PROPERTY_NTOOLS = 'số công cụ';
var PROPERTY_AHIDE = 'ẩn tất cả khi giải';
var SCRAMBLE_LAST = 'trước';
var SCRAMBLE_NEXT = 'sau';
var SCRAMBLE_SCRAMBLE = ' tráo';
var SCRAMBLE_SCRAMBLING = 'Tráo';
var SCRAMBLE_LENGTH = 'độ dài';
var SCRAMBLE_INPUT = 'Nhập scramble';
var SCRAMBLE_INPUTTYPE = 'Scramble type';
var PROPERTY_VRCSPEED = 'Tốc độ ban đầu của rubik ảo (tốc độ xoay ban đầu)';
var PROPERTY_VRCORI = 'Virtual cube orientation';
var PROPERTY_VRCMP = 'nhiều đợt';
var PROPERTY_VRCMPS = 'Không có|CFOP|CF+OP|CFFFFOP|CFFFFOOPP|Roux';
var PROPERTY_GIIKERVRC = 'Hiển thị rubik bluetooth ảo';
var PROPERTY_GIISOK_DELAY = 'Xem như đã tráo rubik nếu đứng yên trong';
var PROPERTY_GIISOK_DELAYS = '2s|3s|4s|5s|Không bao giờ|Đã tráo một cách chính xác';
var PROPERTY_GIISOK_KEY = 'Xem như đã tráo bằng cách nhấn phím cách';
var PROPERTY_GIISOK_MOVE = 'Xem như đã tráo bằng cách thực hiện';
var PROPERTY_GIISOK_MOVES = 'U4, R4,...|(U U\')2, (U\' U)2,...|Không bao giờ';
var PROPERTY_GIISBEEP = 'Phát tiếng bíp khi tráo xong';
var PROPERTY_GIIRST = 'Khởi động lại rubik bluetooth khi kết nối';
var PROPERTY_GIIRSTS = 'Luôn luôn|Nhắc tôi|Không bao giờ';
var PROPERTY_GIIMODE = 'Bluetooth Cube Mode';
var PROPERTY_GIIMODES = 'Normal|Training|Continuous training';
var PROPERTY_VRCAH = 'Useless pieces in huge cube';
var PROPERTY_VRCAHS = 'Hide|Border|Color|Show';
var CONFIRM_GIIRST = 'Khởi động lại rubik bluetooth khi giải xong?';
var PROPERTY_GIIAED = 'Tự động phát hiện lỗi phần cứng';
var scrdata = [
	['WCA', [
		['3x3x3', "333", 0],
		['2x2x2', "222so", 0],
		['4x4x4', "444wca", -40],
		['5x5x5', "555wca", -60],
		['6x6x6', "666wca", -80],
		['7x7x7', "777wca", -100],
		['3x3 BLD', "333ni", 0],
		['3x3 FM', "333fm", 0],
		['3x3 OH', "333oh", 0],
		['clock', "clkwca", 0],
		['megaminx', "mgmp", -70],
		['pyraminx', "pyrso", -10],
		['skewb', "skbso", 0],
		['Square-1', "sqrs", 0],
		['4x4 bld', "444bld", -40],
		['5x5 bld', "555bld", -60],
		['3x3 mbld', "r3ni", 5]
	]],
	['Nhập', [
		['外部', "input", 0],
		['Competition', "remoteComp", 0],
		['Online battle', "remoteBattle", 0],
		['Remote', "remoteOther", 0]
	]],
	['===WCA===', [
		['--', "blank", 0]
	]],
	['3x3x3', [
		["random state (WCA)", "333", 0],
		['random move', "333o", 25],
		['3x3x3 cho những thằng đần', "333noob", 25],
		['cạnh', "edges", 0],
		['góc', "corners", 0],
		['Hỗ trợ giải bịt mắt', "nocache_333bldspec", 0],
		['Pattern Tool', "nocache_333patspec", 0],
		['3x3 FT', "333ft", 0],
		['Custom', "333custom", 0]
	]],
	['3x3x3 CFOP', [
		['PLL', "pll", 0],
		['OLL', "oll", 0],
		['F2L cuối  + tầng 3', "lsll2", 0],
		['tầng 3', "ll", 0],
		['ZBLL', "zbll", 0],
		['COLL', "coll", 0],
		['CLL', "cll", 0],
		['ELL', "ell", 0],
		['2GLL', "2gll", 0],
		['ZZLL', "zzll", 0],
		['ZBLS', "zbls", 0],
		['EOLS', "eols", 0],
		['WVLS', "wvls", 0],
		['VLS', "vls", 0],
		['đã giải cross', "f2l", 0],
		['Edge Orientation Line', "eoline", 0],
		['EO Cross', "eocross", 0],
		['solve dởm', "easyc", 3],
		['Xcross dễ', "easyxc", 4]
	]],
	['3x3x3 Roux', [
		['2nd Block', "sbrx", 0],
		['CMLL', "cmll", 0],
		['LSE', "lse", 0],
		['LSE &lt;M, U&gt;', "lsemu", 0]
	]],
	['3x3x3 Mehta', [
		['3QB', "mt3qb", 0],
		['EOLE', "mteole", 0],
		['TDR', "mttdr", 0],
		['6CP', "mt6cp", 0],
		['CDRLL', "mtcdrll", 0],
		['L5EP', "mtl5ep", 0],
		['TTLL', "ttll", 0]
	]],
	['2x2x2', [
		["random state (WCA)", "222so", 0],
		['optimal', "222o", 0],
		['3-gen', "2223", 25],
		['EG', "222eg", 0],
		['CLL', "222eg0", 0],
		['EG1', "222eg1", 0],
		['EG2', "222eg2", 0],
		['TCLL+', "222tcp", 0],
		['TCLL-', "222tcn", 0],
		['TCLL', "222tc", 0],
		['LS', "222lsall", 0],
		['No Bar', "222nb", 0]
	]],
	['4x4x4', [
		["WCA", "444wca", -40],
		['Ngẫu nhiên', "444m", 40],
		['SiGN', "444", 40],
		['YJ', "444yj", 40],
		['4x4x4 cạnh', "4edge", 0],
		['Chỉ dùng R,r,U,u để tráo', "RrUu", 40],
		['Last layer', "444ll", 0],
		['ELL', "444ell", 0],
		['Edge only', "444edo", 0],
		['Center only', "444cto", 0]
	]],
	['4x4x4 Yau/Hoya', [
		['UD center solved', "444ctud", 0],
		['UD+3E solved', "444ud3c", 0],
		['Last 8 dedges', "444l8e", 0],
		['RL center solved', "444ctrl", 0],
		['RLDX center solved', "444rlda", 0],
		['RLDX cross solved', "444rlca", 0]
	]],
	['5x5x5', [
		["WCA", "555wca", 60],
		['SiGN', "555", 60],
		['5x5x5 cạnh', "5edge", 8]
	]],
	['6x6x6', [
		["WCA", "666wca", 80],
		['SiGN', "666si", 80],
		['prefix', "666p", 80],
		['suffix', "666s", 80],
		['6x6x6 cạnh', "6edge", 8]
	]],
	['7x7x7', [
		["WCA", "777wca", 100],
		['SiGN', "777si", 100],
		['prefix', "777p", 100],
		['suffix', "777s", 100],
		['7x7x7 cạnh', "7edge", 8]
	]],
	['Rubik Clock', [
		['WCA', "clkwca", 0],
		['Hiệp hội Rubik Thế giới (old)', "clkwcab", 0],
		['WCA w/o y2', "clknf", 0],
		['jaap', "clk", 0],
		['optimal', "clko", 0],
		['Ngắn Gọn', "clkc", 0],
		['efficient pin order', "clke", 0]
	]],
	['Megaminx', [
		["WCA", "mgmp", 70],
		['Cà rốt', "mgmc", 70],
		['Kiểu cũ', "mgmo", 70],
		['2-gen R,U', "minx2g", 30],
		['last slot + tầng cuối', "mlsll", 0],
		['PLL', "mgmpll", 0],
		['Last Layer', "mgmll", 0]
	]],
	['Rubik kim tử tháp (Pyraminx)', [
		["random state (WCA)", "pyrso", 10],
		['optimal', "pyro", 0],
		['random move', "pyrm", 25],
		['L4E', "pyrl4e", 0],
		['4 tips', "pyr4c", 0],
		['No bar', "pyrnb", 0]
	]],
	['Skewb', [
		["random state (WCA)", "skbso", 0],
		['optimal', "skbo", 0],
		['random move', "skb", 25],
		['No bar', "skbnb", 0]
	]],
	['Rubik square ', [
		["random state (WCA)", "sqrs", 0],
		["CSP", "sqrcsp", 0],
		["PLL", "sq1pll", 0],
		['face turn metric', "sq1h", 40],
		['twist metric', "sq1t", 20]
	]],
	['===KHÁC===', [
		['--', "blank", 0]
	]],
	['Sắp xếp 15 số', [
		['random state URLD', "15prp", 0],
		['random state ^<>v', "15prap", 0],
		['random state Blank', "15prmp", 0],
		['random move URLD', "15p", 80],
		['random move ^<>v', "15pat", 80],
		['random move Blank', "15pm", 80]
	]],
	['8 puzzle', [
		['random state URLD', "8prp", 0],
		['random state ^<>v', "8prap", 0],
		['random state Blank', "8prmp", 0]
	]],
	['LxMxN', [
		['1x3x3', "133", 0],
		['2x2x3', "223", 0],
		['2x3x3', "233", 25],
		['3x3x4', "334", 40],
		['3x3x5', "335", 25],
		['3x3x6', "336", 40],
		['3x3x7', "337", 40],
		['8x8x8', "888", 120],
		['9x9x9', "999", 120],
		['10x10x10', "101010", 120],
		['11x11x11', "111111", 120],
		['NxNxN', "cubennn", 12]
	]],
	['Gear Cube', [
		['random state', "gearso", 0],
		['optimal', "gearo", 0],
		['random move', "gear", 10]
	]],
	['Kilominx', [
		['random state', "klmso", 0],
		['Pochmann', "klmp", 30]
	]],
	['Gigaminx', [
		['Pochman', "giga", 300]
	]],
	['Crazy Puzzle', [
		['Crazy 3x3x3', "crz3a", 30]
	]],
	['Cmetrick', [
		['Cmetrick', "cm3", 25],
		['Cmetrick Mini', "cm2", 25]
	]],
	['Helicopter Cube', [
		['Heli copter', "heli", 40],
		['Curvy copter', "helicv", 40],
		['2x2 Heli random move', "heli2x2", 70],
		['2x2 Heli by group', "heli2x2g", 5]
	]],
	['Redi Cube', [
		['random state', "rediso", 0],
		['Moyu', "redim", 8],
		['random move', "redi", 20]
	]],
	['Dino Cube', [
		['random state', "dinoso", 0],
		['optimal', "dinoo", 0]
	]],
	['Rubik Ivy', [
		['random state', "ivyso", 0],
		['optimal', "ivyo", 0],
		['random move', "ivy", 10]
	]],
	['Master Pyraminx', [
		['random state', "mpyrso", 0],
		['random move', "mpyr", 42]
	]],
	['Pyraminxx Crystal', [
		['Pochmann', "prcp", 70],
		['Kiểu cũ', "prco", 70]
	]],
	['Khối vuông kiểu Xiêm', [
		['khối 1x1x3 ', "sia113", 25],
		['khối 1x2x3', "sia123", 25],
		['khối 2x2x2', "sia222", 25]
	]],
	['Square', [
		['Square-2', "sq2", 20],
		['Super Square-1', "ssq1t", 20]
	]],
	['Super Floppy', [
		[' ', "sfl", 25]
	]],
	['UFO', [
		['kiểu Jaap', "ufo", 25]
	]],
	['FTO (Face-Turning Octahedron)', [
		['random state', "ftoso", 0],
		['random move', "fto", 30],
		['L3T', "ftol3t", 0],
		['L3T+LBT', "ftol4t", 0],
		['TCP', "ftotcp", 0],
		['edges only', "ftoedge", 0],
		['centers only', "ftocent", 0],
		['corners only', "ftocorn", 0],
		['Diamond random state', "dmdso", 0]
	]],
	['Icosahedron', [
		['Icosamate random move', "ctico", 60]
	]],
	['===ĐẶC BIỆT===', [
		['--', "blank", 0]
	]],
	['3x3x3 subsets', [
		['2-gen R,U', "2gen", 0],
		['2-gen L,U', "2genl", 0],
		['Roux-2gen M,U', "roux", 0],
		['3-gen F,R,U', "3gen_F", 0],
		['3-gen R,U,L', "3gen_L", 0],
		['3-gen R,r,U', "RrU", 0],
		['Domino Subgroup', "333drud", 0],
		['không có bước kép', "half", 0],
		['F2L cuối + tầng 3 ( kiểu cũ )', "lsll", 15]
	]],
	['Bandaged Cube', [
		['Bicube', "bic", 30],
		['Square-1 /,(1,0)', "bsq", 25]
	]],
	['Relays', [
		['Nhiều 3x3x3s', "r3", 5],
		['Tiếp sức 234', "r234", 0],
		['2345 relay', "r2345", 0],
		['23456 relay', "r23456", 0],
		['234567 relay', "r234567", 0],
		['Tiếp sức 234 (WCA)', "r234w", 0],
		['2345 relay (WCA)', "r2345w", 0],
		['23456 relay (WCA)', "r23456w", 0],
		['234567 relay (WCA)', "r234567w", 0],
		['Mini Guildford', "rmngf", 0]
	]],
	['=== VUI =)) ===', [
		['--', "blank", 0]
	]],
	['1x1x1', [
		['x y z', "111", 25]
	]],
	['-1x-1x-1', [
		[' ', "-1", 25]
	]],
	['1x1x2', [
		[' ', "112", 25]
	]],
	['LOL', [
		[' ', "lol", 25]
	]],
	['Derrick Eide', [
		[' ', "eide", 25]
	]]
];
var SCRAMBLE_NOOBST = [
	['Quay mặt trên cùng', 'Quay mặt dưới'],
	['Quay mặt phải', 'Quay mặt trái'],
	['Quay mặt trước mặt', 'Quay mặt đằng sau']
];
var SCRAMBLE_NOOBSS = ' quay theo chiều kim đồn hồ 90 độ,| quay ngược theo chiều kim đồn hồ 90 độ,| quay 180 độ,';
var SCROPT_TITLE = 'Cài đặt cách tráo';
var SCROPT_BTNALL = 'Full';
var SCROPT_BTNNONE = 'Clear';
var SCROPT_EMPTYALT = 'Please select at least one case';
var STATS_CFM_RESET = 'bạn có chắc muốn xóa mọi thời gian trong mục này?';
var STATS_CFM_DELSS = 'delete session [%s]?';
var STATS_CFM_DELMUL = 'Số lần giải cần xóa trong phiên hiện tại?';
var STATS_CFM_DELETE = 'Xóa thời gian này?';
var STATS_COMMENT = 'bình luận';
var STATS_REVIEW = 'Xem xét lại';
var STATS_DATE = 'Date';
var STATS_SSSTAT = '1-solve stat.';
var STATS_SSRETRY = 'Retry';
var STATS_CURROUND = 'thành tích vòng hiện tại';
var STATS_CURSESSION = 'thành tích mục hiện tại';
var STATS_CURSPLIT = 'Phase %d of Current Session Statistics';
var STATS_EXPORTCSV = 'Export CSV';
var STATS_SSMGR_TITLE = 'Session Manager';
var STATS_SSMGR_NAME = 'Name';
var STATS_SSMGR_DETAIL = 'Chi tiết phiên';
var STATS_SSMGR_OPS = 'Rename|Create|Split|Merge|Delete|Sort|Merge&Dedupe';
var STATS_SSMGR_ORDER = 'Số thứ tự bằng cách tráo';
var STATS_SSMGR_ODCFM = 'Sort all sessions by scramble?';
var STATS_SSMGR_SORTCFM = '%d lần giải sẽ được xắp xếp lại,đồng ý?';
var STATS_ALERTMG = 'Merge all times in session [%f] to the end of session [%t]?';
var STATS_PROMPTSPL = 'Number of latest times split from session [%s]?';
var STATS_ALERTSPL = 'Should split or leave 1 time at least';
var STATS_AVG = 'mean';
var STATS_SUM = 'sum';
var STATS_SOLVE = 'lượt giải';
var STATS_TIME = 'thời gian';
var STATS_SESSION = 'Session';
var STATS_SESSION_NAME = 'Edit session name';
var STATS_SESSION_NAMEC = 'Tên của lượt mới';
var STATS_STRING = 'best|vừa xong|worst|tạo ra bởi csTimer on %Y-%M-%D|solves/tổng: %d|single|mean of %mk|avg of %mk|Average: %v{ (σ = %sgm)}|Mean: %v|Thành tích:|solving from %s to %e|Totally spent: %d|target';
var STATS_PREC = 'time distribution precision';
var STATS_PREC_STR = 'tự động|0.1s|0.2s|0.5s|1s|2s|5s|10s|20s|50s|100s';
var STATS_TYPELEN = 'list %d type|list %d length|average|mean';
var STATS_STATCLR = 'Enable session emptying';
var STATS_ABSIDX = 'Hiển thị chỉ số tuyệt đối trong thống kê';
var STATS_XSESSION_DATE = 'any date|past 24 hours|past 7 days|past 30 days|past 365 days';
var STATS_XSESSION_NAME = 'Tên bất kì';
var STATS_XSESSION_SCR = 'Tráo bất kì';
var STATS_XSESSION_CALC = 'Tính';
var STATS_RSFORSS = 'Show stat. when clicking solve number';
var PROPERTY_PRINTSCR = 'In scramble trong thành tích';
var PROPERTY_PRINTCOMM = 'print comment(s) in statistics';
var PROPERTY_PRINTDATE = 'in ngày giải trong thống kê';
var PROPERTY_SUMMARY = 'cho xem bảng thành tích	';
var PROPERTY_IMRENAME = 'yêu cầu đặt tên ngay sau khi tạo 1 mục';
var PROPERTY_SCR2SS = 'tạo ngay 1 mục khi chuyển sang scramble khác';
var PROPERTY_SS2SCR = 'không xóa scramble khi chuyển sang mục khác';
var PROPERTY_SS2PHASES = 'restore multi-phase timing when switching session';
var PROPERTY_STATINV = 'Inverse time list';
var PROPERTY_STATSSUM = 'Show sum in time list';
var PROPERTY_STATTHRES = 'Show target time for session best';
var PROPERTY_STATBPA = 'Show best possible average (BPA)';
var PROPERTY_STATWPA = 'Show worst possible average (WPA)';
var PROPERTY_STATAL = 'Statistical indicators';
var PROPERTY_STATALU = 'Customized statistical indicator';
var PROPERTY_HLPBS = 'Highlight PBs';
var PROPERTY_HLPBS_STR = 'Dark orange as WCA|As link color|Bolder|None';
var PROPERTY_DELMUL = 'cho phép xóa thời gian cùng 1 lúc';
var PROPERTY_TOOLSFUNC = 'Chức Năng Được Chọn';
var PROPERTY_TRIM = 'Number of solves trimmed at better side';
var PROPERTY_TRIMR = 'Number of solves trimmed at worse side';
var PROPERTY_TRIM_MED = 'Trung bình';
var PROPERTY_STKHEAD = 'Use Stackmat Status Information';
var PROPERTY_TOOLPOS = 'Tools panel position';
var PROPERTY_TOOLPOS_STR = 'Dưới|Giữa|Trên';
var PROPERTY_HIDEFULLSOL = 'Hiện cách giải theo thứ tự';
var PROPERTY_IMPPREV = 'Thêm vào dữ liệu cũ';
var PROPERTY_AUTOEXP = 'Tự Động Xuất (mỗi 100 lượt giải)';
var PROPERTY_AUTOEXP_OPT = 'Never|To File|With csTimer ID|With WCA Account|With Google Account|Alert Only';
var PROPERTY_SCRASIZE = 'Auto scramble size';
var MODULE_NAMES = {
	"kernel": 'toàn cầu',
	"ui": 'màn hình chính ',
	"color": 'màu',
	"timer": 'đồng hồ đếm giờ',
	"scramble": 'xáo trộn',
	"stats": 'thành tích',
	"tools": 'công cụ',
	"vrc": 'ảo&<br>bluetooth'
};
var BGIMAGE_URL = 'Yêu cầu nhập URL của ảnh';
var BGIMAGE_INVALID = 'URL không hợp lệ';
var BGIMAGE_OPACITY = 'Background';
var BGIMAGE_IMAGE = 'background';
var BGIMAGE_IMAGE_STR = 'none|tự chọn|CCT';
var SHOW_AVG_LABEL = 'cho xem avg ở dưới timer';
var SHOW_DIFF_LABEL = 'Show Difference Label';
var SHOW_DIFF_LABEL_STR = '-Green+Red|-Red+Green|Normal|None';
var USE_LOGOHINT = 'Hint messages in logo';
var TOOLS_SCRGEN = 'ScrambleGenerator';
var SCRGEN_NSCR = 'số scrambles';
var SCRGEN_PRE = 'prefix';
var SCRGEN_GEN = 'Tạo Xáo Trộn';
var VRCREPLAY_TITLE = 'Virtual Replay';
var VRCREPLAY_ORI = 'raw ori|auto ori';
var VRCREPLAY_SHARE = 'share link';
var GIIKER_CONNECT = 'Click to connect';
var GIIKER_RESET = 'Reset (Mark Solved)';
var GIIKER_REQMACMSG = 'Please enter the MAC address of your smart hardware (xx:xx:xx:xx:xx:xx). You can find the MAC address through chrome://bluetooth-internals/#devices, or modify following options to let csTimer automatically obtain it:\nChrome: Turn on chrome://flags/#enable-experimental-web-platform-features\nBluefy: Turn on Enable BLE Advertisements';
var GIIKER_NOBLEMSG = 'Bluetooth API is not available. Ensure https access, check bluetooth is enabled on your device, and try chrome with chrome://flags/#enable-experimental-web-platform-features enabled';
var PROPERTY_SHOWAD = 'Show advertisements (take effect after reload)';
var PROPERTY_GIIORI = 'Cube orientation';
var LGHINT_INVALID = 'Invalid Value!';
var LGHINT_NETERR = 'Network Error!';
var LGHINT_SERVERR = 'Server Error!';
var LGHINT_SUBMITED = 'Submitted';
var LGHINT_SSBEST = 'Session best %s!';
var LGHINT_SCRCOPY = 'Scramble copied';
var LGHINT_LINKCOPY = 'Share link copied';
var LGHINT_SOLVCOPY = 'Solve copied';
var LGHINT_SORT0 = 'Already sorted';
var LGHINT_IMPORTED = 'Import %d session(s)';
var LGHINT_IMPORT0 = 'No session imported';
var LGHINT_BTCONSUC = 'Bluetooth successfully connected';
var LGHINT_BTDISCON = 'Bluetooth disconnected';
var LGHINT_BTNOTSUP = 'Not support your smart cube';
var LGHINT_BTINVMAC = 'Not a valid mac address, cannot connect to your smart cube';
var LGHINT_AEXPABT = 'Auto export abort';
var LGHINT_AEXPSUC = 'Auto export success';
var LGHINT_AEXPFAL = 'Auto export failed';
var EASY_SCRAMBLE_HINT = 'Change length to limit upper bound of solution length, input 2 digits to limit both lower (<= 8) and upper bound';
