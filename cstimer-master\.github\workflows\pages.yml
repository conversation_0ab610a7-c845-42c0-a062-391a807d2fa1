name: Deploy to Github Pages

on:
  push:
    branches: ["master"]

  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup java
        uses: actions/setup-java@v4.0.0
        with: 
          distribution: 'temurin'
          java-version: '11'
      - name: Setup php
        uses: shivammathur/setup-php@2.28.0
        with:
          php-version: '8.0'
      - name: Build cstimer
        run: mkdir dist/local dist/local/js dist/local/css && make local
      - name: Setup Pages
        uses: actions/configure-pages@v4
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: 'dist/local'
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
