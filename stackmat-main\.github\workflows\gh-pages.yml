name: Demo to gh-pages 

on:
  push:
    branches:
      - main
    paths:
      - demo/**

jobs:
  publish-gh-pages:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./demo
    steps:
      - name: Checkout project
        uses: actions/checkout@v3
      - name: Use Node.js v16
        uses: actions/setup-node@v3
        with:
          node-version: "16"
          registry-url: https://registry.npmjs.org/
      - name: Install dependencies
        run: npm ci
      - name: Build Vue app
        run: npm run build:pages
      - name: Push Vue app to gh-pages branch
        run: |
              cd dist
              ln -s index.html 404.html
              git config --global user.email "<EMAIL>"
              git config --global user.name "ci"
              git init
              git add -A
              git commit -m "deploy"
              git push -f https://${{ secrets.GH_ACCESS_TOKEN }}@github.com/stilesdev/stackmat.git HEAD:gh-pages
