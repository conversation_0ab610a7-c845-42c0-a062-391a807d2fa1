var OK_LANG = 'Ok';
var CANCEL_LANG = 'Cancelar';
var RESET_LANG = 'Reiniciar';
var ABOUT_LANG = 'Sobre';
var ZOOM_LANG = 'Ampliar';
var COPY_LANG = 'Copy';
var BUTTON_TIME_LIST = 'LISTAR<br>TEMPOS';
var BUTTON_OPTIONS = 'OPÇÕES';
var BUTTON_EXPORT = 'EXPORTAR';
var BUTTON_DONATE = 'DOAR';
var PROPERTY_SR = 'Com sessão';
var PROPERTY_USEINS = 'usar inspeção da WCA';
var PROPERTY_USEINS_STR = 'Always (down)|Always (up)|Except BLD (down)|Except BLD (up)|Never';
var PROPERTY_SHOWINS = 'Mostrar um ícone quando a inspeção estiver habilitada';
var PROPERTY_VOICEINS = 'aviso de voz da inspeção da WCA';
var PROPERTY_VOICEINS_STR = 'nenhuma|voz masculina|voz feminina';
var PROPERTY_VOICEVOL = 'Volume de voz';
var PROPERTY_PHASES = 'fases de cronometragem';
var PROPERTY_TIMERSIZE = 'tamanho do cronômetro';
var PROPERTY_USEMILLI = 'usar milisegundos';
var PROPERTY_SMALLADP = 'usar fonte pequena depois do ponto';
var PROPERTY_SCRSIZE = 'tamanho do embaralhamento';
var PROPERTY_SCRMONO = 'embaralhamento com espaços';
var PROPERTY_SCRLIM = 'Limitar a altura da área de embaralhamento';
var PROPERTY_SCRALIGN = 'Alinhamento da área do embaralhamento';
var PROPERTY_SCRALIGN_STR = 'centro|esquerda|direita';
var PROPERTY_SCRWRAP = 'Scramble Wrap';
var PROPERTY_SCRWRAP_STR = 'Balanced|Normal';
var PROPERTY_SCRNEUT = 'Color neutral';
var PROPERTY_SCRNEUT_STR = 'None|Single face|Double faces|Six faces';
var PROPERTY_SCREQPR = 'Probabilities for training-scramble states';
var PROPERTY_SCREQPR_STR = 'Actual|Equal|Random order';
var PROPERTY_SCRFAST = 'Usando embaralhamento rápido para 4x4x4 (não-oficial)';
var PROPERTY_SCRKEYM = 'Destacar movimento(s) chave';
var PROPERTY_SCRCLK = 'Action when clicking scramble';
var PROPERTY_SCRCLK_STR = 'None|Copy|Next scramble';
var PROPERTY_WNDSCR = 'Estilo do painel de exibição do embaralhamento';
var PROPERTY_WNDSTAT = 'Estilo do painel de estatísticas';
var PROPERTY_WNDTOOL = 'Estilo do painel de ferramentas';
var PROPERTY_WND_STR = 'Normal|Reduzido';
var EXPORT_DATAEXPORT = 'Dados Importar/Exportar';
var EXPORT_TOFILE = 'Exportar como arquivo';
var EXPORT_FROMFILE = 'Importar arquivo';
var EXPORT_TOSERV = 'Exportar para o servidor';
var EXPORT_FROMSERV = 'Importar do servidor';
var EXPORT_FROMOTHER = 'Importar sessão(ões) de outros timers';
var EXPORT_USERID = 'Por favor coloque sua conta (apenas alfabeto ou números)';
var EXPORT_INVID = 'Apenas alfabeto ou números são permitidos!';
var EXPORT_ERROR = 'Alguns erros ocorreram...';
var EXPORT_NODATA = 'Sem dados encontrados para sua conta';
var EXPORT_UPLOADED = 'Enviado com sucesso';
var EXPORT_CODEPROMPT = 'Save this code, or type saved code to import';
var EXPORT_ONLYOPT = 'Exportar/Importar apenas Opções';
var EXPORT_ACCOUNT = 'Exportar Contas';
var EXPORT_LOGINGGL = 'Acessar usando conta Google';
var EXPORT_LOGINWCA = 'Login usando a conta WCA';
var EXPORT_LOGOUTCFM = 'Confirmar a saída?';
var EXPORT_LOGINAUTHED = 'Autorizado<br>Recuperando Dados...';
var EXPORT_AEXPALERT = 'More than %d solves since last backup';
var EXPORT_WHICH = 'You have %d file(s), which one should be imported?';
var EXPORT_WHICH_ITEM = '%s solve(s), uploaded at %t';
var IMPORT_FINAL_CONFIRM = 'This will override all local data! It will modify %d sessions, add %a and remove %r solves at least. Confirm to import data?';
var BUTTON_SCRAMBLE = 'EMBARA-<br>LHAMENTO';
var BUTTON_TOOLS = 'FERRA-<br>MENTAS';
var IMAGE_UNAVAILABLE = 'Indisponível para esse tipo de embaralhamento!';
var TOOLS_SELECTFUNC = 'Função';
var TOOLS_CROSS = 'resolver a cruz';
var TOOLS_EOLINE = 'resolver a EOLine';
var TOOLS_ROUX1 = 'resolver o Roux S1';
var TOOLS_222FACE = '2x2x2 face';
var TOOLS_GIIKER = 'cubo Giiker';
var TOOLS_IMAGE = 'desenhar o embaralhamento';
var TOOLS_STATS = 'Estatíticas';
var TOOLS_HUGESTATS = 'estatísticas de sessão cruzada';
var TOOLS_DISTRIBUTION = 'Distribuição dos tempos';
var TOOLS_TREND = 'Gráfico dos tempos';
var TOOLS_METRONOME = 'Metrônomo';
var TOOLS_RECONS = 'Reconstruir';
var TOOLS_RECONS_NODATA = 'No solution found.';
var TOOLS_RECONS_TITLE = 'insp|exec|turn|tps';
var TOOLS_TRAINSTAT = 'Status do treinamento.';
var TOOLS_BLDHELPER = 'Auxiliar de BLD';
var TOOLS_CFMTIME = 'Confirmar tempo';
var TOOLS_SOLVERS = 'Solvers';
var TOOLS_DLYSTAT = 'Daily Statistics';
var TOOLS_DLYSTAT1 = 'Period|Start of Day|Week';
var TOOLS_DLYSTAT_OPT1 = 'day|week|month|year';
var TOOLS_DLYSTAT_OPT2 = 'Sun|Mon|Tue|Wed|Thu|Fri|Sat';
var TOOLS_SYNCSEED = 'Common Scramble';
var TOOLS_SYNCSEED_SEED = 'Seed';
var TOOLS_SYNCSEED_INPUT = 'Input Seed';
var TOOLS_SYNCSEED_30S = 'Use 30s Seed';
var TOOLS_SYNCSEED_HELP = 'Se ativado, o embaralhamento dependerá apenas das configurações de chave e embaralhamento.';
var TOOLS_SYNCSEED_DISABLE = 'Disable current seed?';
var TOOLS_SYNCSEED_INPUTA = 'Input a value (a-zA-Z0-9) as seed';
var TOOLS_BATTLE = 'Online battle';
var TOOLS_BATTLE_HEAD = 'Room|Join Room';
var TOOLS_BATTLE_TITLE = 'Rank|Status|Time';
var TOOLS_BATTLE_STATUS = 'Ready|Inspect|Solving|Solved|Lost';
var TOOLS_BATTLE_INFO = 'Join a battle room with your friend, then you will battle together.';
var TOOLS_BATTLE_JOINALERT = 'Please input the room ID';
var TOOLS_BATTLE_LEAVEALERT = 'Leave current room';
var OLCOMP_UPDATELIST = 'Update Competition List';
var OLCOMP_VIEWRESULT = 'Ver Resultado';
var OLCOMP_VIEWMYRESULT = 'Meu Histórico';
var OLCOMP_START = 'Iniciar!';
var OLCOMP_SUBMIT = 'Submit!';
var OLCOMP_SUBMITAS = 'Enviar Como: ';
var OLCOMP_WCANOTICE = 'Submit As Your WCA Account? (Relogin if not recognized after submitting)';
var OLCOMP_OLCOMP = 'Online Competition';
var OLCOMP_ANONYM = 'Anônimo';
var OLCOMP_ME = 'Eu';
var OLCOMP_WCAACCOUNT = 'Conta WCA';
var OLCOMP_ABORT = 'Abort competition and show results?';
var OLCOMP_WITHANONYM = 'Com Perfil Anônimo';
var PROPERTY_IMGSIZE = 'Tamanho da imagem do embaralhamento';
var PROPERTY_IMGREP = 'Show virtual cube animation when clicking scramble image';
var TIMER_INSPECT = 'inspecionando...';
var TIMER_SOLVE = 'resolvendo!';
var PROPERTY_USEMOUSE = 'usar mouse como cronômetro';
var PROPERTY_TIMEU = 'atualização do cronômetro';
var PROPERTY_TIMEU_STR = 'padrão|0.1s|segundos|inspeção|nenhuma';
var PROPERTY_PRETIME = 'tempo mantendo a barra de espaços pressionada (segundo(s))';
var PROPERTY_ENTERING = 'inserção de tempos';
var PROPERTY_ENTERING_STR = 'cronômetro|digitação|stackmat|Timer da MoYu|puzzle virtual|Bluetooth|qCube|GanTimer|last layer training';
var PROPERTY_INTUNIT = 'Unit when entering an integer';
var PROPERTY_INTUNIT_STR = 'second|centisecond|millisecond';
var PROPERTY_COLOR = 'escolha um tema de cores';
var PROPERTY_COLORS = 'cor da fonte|cor do fundo|cor da borda|cor do botгo|cor dos links|Cor do logotipo|cor de fundo do logotipo';
var PROPERTY_VIEW = 'formato do site';
var PROPERTY_VIEW_STR = 'Automático|Mobile|Desktop';
var PROPERTY_UIDESIGN = 'UI design is';
var PROPERTY_UIDESIGN_STR = 'Normal|Material design|Normal w/o shadows|Material design w/o shadows';
var COLOR_EXPORT = 'Por favor, salve a String para importar';
var COLOR_IMPORT = 'Por favor, insira a String que foi exportada';
var COLOR_FAIL = 'Informação Incorreta. A importação falhou! :(';
var PROPERTY_FONTCOLOR_STR = 'preto|branco';
var PROPERTY_COLOR_STR = 'manual|importar/exportar...|aleatório|estilo 1|estilo 2|estilo 3|preto|branco|estilo 6|solarized dark|solarized light';
var PROPERTY_FONT = 'fonte do cronômetro';
var PROPERTY_FONT_STR = 'digital aleatória|normal|digital 1|digital 2|digital 3|digital 4|digital 5';
var PROPERTY_FORMAT = 'formato do tempo';
var PROPERTY_USEKSC = 'usar atalhos do teclado';
var PROPERTY_USEGES = 'use gesture control';
var PROPERTY_NTOOLS = 'número de ferramentas';
var PROPERTY_AHIDE = 'Esconder TODOS os elementos enquanto se resolve';
var SCRAMBLE_LAST = 'último';
var SCRAMBLE_NEXT = 'próximo';
var SCRAMBLE_SCRAMBLE = ' embaralhamento';
var SCRAMBLE_SCRAMBLING = 'Embaralhamento';
var SCRAMBLE_LENGTH = 'tamanho';
var SCRAMBLE_INPUT = 'colocar embaralhamento(s)';
var SCRAMBLE_INPUTTYPE = 'Scramble type';
var PROPERTY_VRCSPEED = 'velocidade base do VRC (tps)';
var PROPERTY_VRCORI = 'Virtual cube orientation';
var PROPERTY_VRCMP = 'múltiplas fases';
var PROPERTY_VRCMPS = 'Nenhum|CFOP|CF+OP|CFFFFOP|CFFFFOOPP|Roux';
var PROPERTY_GIIKERVRC = 'Mostrar cubo Giiker virtual';
var PROPERTY_GIISOK_DELAY = 'Indicar o final do embaralhamento esperando';
var PROPERTY_GIISOK_DELAYS = '2s|3s|4s|5s|Nunca|Embaralhado corretamente';
var PROPERTY_GIISOK_KEY = 'indicar o final do embaralhamento com a barra de espaço';
var PROPERTY_GIISOK_MOVE = 'Indicar o final do embaralhamento fazendo';
var PROPERTY_GIISOK_MOVES = 'U4, R4, etc|(U U\')2, (U\' U)2, etc|Nunca';
var PROPERTY_GIISBEEP = 'Apitar quando estiver embaralhado';
var PROPERTY_GIIRST = 'Resetar cubo Giiker quando conectado';
var PROPERTY_GIIRSTS = 'Sempre|Perguntar|Nunca';
var PROPERTY_GIIMODE = 'Bluetooth Cube Mode';
var PROPERTY_GIIMODES = 'Normal|Training|Continuous training';
var PROPERTY_VRCAH = 'Useless pieces in huge cube';
var PROPERTY_VRCAHS = 'Hide|Border|Color|Show';
var CONFIRM_GIIRST = 'Reiniciar cubo Giiker assim que for resolvido?';
var PROPERTY_GIIAED = 'Detecção de erro de hardware automática';
var scrdata = [
	['WCA', [
		['3x3x3', "333", 0],
		['2x2x2', "222so", 0],
		['4x4x4', "444wca", -40],
		['5x5x5', "555wca", -60],
		['6x6x6', "666wca", -80],
		['7x7x7', "777wca", -100],
		['3x3x3 BLD', "333ni", 0],
		['3x3x3 FM', "333fm", 0],
		['3x3x3 OH', "333oh", 0],
		['Clock', "clkwca", 0],
		['Megaminx', "mgmp", -70],
		['Pyraminx', "pyrso", -10],
		['Skewb', "skbso", 0],
		['Square-1', "sqrs", 0],
		['4x4x4 BLD', "444bld", -40],
		['5x5x5 BLD', "555bld", -60],
		['3x3x3 multi BLD', "r3ni", 5]
	]],
	['Entrada', [
		['??', "input", 0],
		['Competição', "remoteComp", 0],
		['Online battle', "remoteBattle", 0],
		['Remote', "remoteOther", 0]
	]],
	['===WCA===', [
		['--', "blank", 0]
	]],
	['3x3x3', [
		["random state (WCA)", "333", 0],
		['movimento aleatório', "333o", 25],
		['3x3x3 para novatos', "333noob", 25],
		['Apenas meios', "edges", 0],
		['Apenas cantos', "corners", 0],
		['Auxiliar de BLD', "nocache_333bldspec", 0],
		['Pattern Tool', "nocache_333patspec", 0],
		['3x3x3 com os pés', "333ft", 0],
		['Custom', "333custom", 0]
	]],
	['3x3x3 CFOP', [
		['PLL', "pll", 0],
		['OLL', "oll", 0],
		['último slot + última camada', "lsll2", 0],
		['última camada', "ll", 0],
		['ZBLL', "zbll", 0],
		['COLL', "coll", 0],
		['CLL', "cll", 0],
		['ELL', "ell", 0],
		['2GLL', "2gll", 0],
		['ZZLL', "zzll", 0],
		['ZBLS', "zbls", 0],
		['EOLS', "eols", 0],
		['WVLS', "wvls", 0],
		['VLS', "vls", 0],
		['cruz resolvida', "f2l", 0],
		['EOLine', "eoline", 0],
		['EO Cross', "eocross", 0],
		['cruz fácil', "easyc", 3],
		['Xcross fácil', "easyxc", 4]
	]],
	['3x3x3 Roux', [
		['2nd Block', "sbrx", 0],
		['CMLL', "cmll", 0],
		['LSE', "lse", 0],
		['LSE &lt;M, U&gt;', "lsemu", 0]
	]],
	['3x3x3 Mehta', [
		['3QB', "mt3qb", 0],
		['EOLE', "mteole", 0],
		['TDR', "mttdr", 0],
		['6CP', "mt6cp", 0],
		['CDRLL', "mtcdrll", 0],
		['L5EP', "mtl5ep", 0],
		['TTLL', "ttll", 0]
	]],
	['2x2x2', [
		["random state (WCA)", "222so", 0],
		['optimal', "222o", 0],
		['3-gen', "2223", 25],
		['EG', "222eg", 0],
		['CLL', "222eg0", 0],
		['EG1', "222eg1", 0],
		['EG2', "222eg2", 0],
		['TCLL+', "222tcp", 0],
		['TCLL-', "222tcn", 0],
		['TCLL', "222tc", 0],
		['LS', "222lsall", 0],
		['No Bar', "222nb", 0]
	]],
	['4x4x4', [
		["WCA", "444wca", -40],
		['movimentos aleatórios', "444m", 40],
		['Criar conta', "444", 40],
		['YJ', "444yj", 40],
		['Meios do 4x4x4', "4edge", 0],
		['R,r,U,u', "RrUu", 40],
		['Last layer', "444ll", 0],
		['ELL', "444ell", 0],
		['Edge only', "444edo", 0],
		['Center only', "444cto", 0]
	]],
	['4x4x4 Yau/Hoya', [
		['UD center solved', "444ctud", 0],
		['UD+3E solved', "444ud3c", 0],
		['Last 8 dedges', "444l8e", 0],
		['RL center solved', "444ctrl", 0],
		['RLDX center solved', "444rlda", 0],
		['RLDX cross solved', "444rlca", 0]
	]],
	['5x5x5', [
		["WCA", "555wca", 60],
		['Criar conta', "555", 60],
		['Meios do 5x5x5', "5edge", 8]
	]],
	['6x6x6', [
		["WCA", "666wca", 80],
		['SiGN', "666si", 80],
		['prefixo', "666p", 80],
		['sufixo', "666s", 80],
		['Meios do 6x6x6', "6edge", 8]
	]],
	['7x7x7', [
		["WCA", "777wca", 100],
		['SiGN', "777si", 100],
		['prefixo', "777p", 100],
		['sufixo', "777s", 100],
		['Meios do 7x7x7', "7edge", 8]
	]],
	['Relógio', [
		['WCA', "clkwca", 0],
		['wca (old)', "clkwcab", 0],
		['WCA w/o y2', "clknf", 0],
		['jaap', "clk", 0],
		['optimal', "clko", 0],
		['conciso', "clkc", 0],
		['efficient pin order', "clke", 0]
	]],
	['Megaminx', [
		["WCA", "mgmp", 70],
		['Carrot', "mgmc", 70],
		['estilo antigo', "mgmo", 70],
		['2-generator R,U', "minx2g", 30],
		['last slot + last layer', "mlsll", 0],
		['PLL', "mgmpll", 0],
		['Last Layer', "mgmll", 0]
	]],
	['Pyraminx', [
		["random state (WCA)", "pyrso", 10],
		['optimal', "pyro", 0],
		['movimento aleatório', "pyrm", 25],
		['L4E', "pyrl4e", 0],
		['4 tips', "pyr4c", 0],
		['No bar', "pyrnb", 0]
	]],
	['Skewb', [
		["random state (WCA)", "skbso", 0],
		['optimal', "skbo", 0],
		['movimento aleatório', "skb", 25],
		['No bar', "skbnb", 0]
	]],
	['Square-1', [
		["random state (WCA)", "sqrs", 0],
		["CSP", "sqrcsp", 0],
		["PLL", "sq1pll", 0],
		['Métrica por Giros de Face', "sq1h", 40],
		['Métrica por Giros de barra', "sq1t", 20]
	]],
	['===OUTRO===', [
		['--', "blank", 0]
	]],
	['15-puzzle (sliding)', [
		['random state URLD', "15prp", 0],
		['random state ^<>v', "15prap", 0],
		['random state Blank', "15prmp", 0],
		['movimento aleatório URLD', "15p", 80],
		['movimento aleatório ^<>v', "15pat", 80],
		['movimento aleatório Blank', "15pm", 80]
	]],
	['8 puzzle', [
		['random state URLD', "8prp", 0],
		['random state ^<>v', "8prap", 0],
		['random state Blank', "8prmp", 0]
	]],
	['LxMxN', [
		['1x3x3 (Cubo Floppy)', "133", 0],
		['2x2x3 (Tower Cube)', "223", 0],
		['2x3x3 (Domino)', "233", 25],
		['3x3x4', "334", 40],
		['3x3x5', "335", 25],
		['3x3x6', "336", 40],
		['3x3x7', "337", 40],
		['8x8x8', "888", 120],
		['9x9x9', "999", 120],
		['10x10x10', "101010", 120],
		['11x11x11', "111111", 120],
		['NxNxN', "cubennn", 12]
	]],
	['Gear Cube', [
		['random state', "gearso", 0],
		['optimal', "gearo", 0],
		['movimento aleatório', "gear", 10]
	]],
	['Kilominx', [
		['random state', "klmso", 0],
		['Pochmann', "klmp", 30]
	]],
	['Gigaminx', [
		['Pochmann', "giga", 300]
	]],
	['Crazy Puzzle', [
		['Crazy 3x3x3', "crz3a", 30]
	]],
	['Cmetrick', [
		['Cmetrick', "cm3", 25],
		['Cmetrick Mini', "cm2", 25]
	]],
	['Helicopter Cube', [
		['Heli copter', "heli", 40],
		['Curvy copter', "helicv", 40],
		['2x2 Heli random move', "heli2x2", 70],
		['2x2 Heli by group', "heli2x2g", 5]
	]],
	['Redi Cube', [
		['random state', "rediso", 0],
		['MoYu', "redim", 8],
		['movimento aleatório', "redi", 20]
	]],
	['Dino Cube', [
		['random state', "dinoso", 0],
		['optimal', "dinoo", 0]
	]],
	['Ivy cube', [
		['random state', "ivyso", 0],
		['optimal', "ivyo", 0],
		['movimento aleatório', "ivy", 10]
	]],
	['Master Pyraminx', [
		['random state', "mpyrso", 0],
		['movimento aleatório', "mpyr", 42]
	]],
	['Pyraminx Crystal', [
		['Pochmann', "prcp", 70],
		['estilo antigo', "prco", 70]
	]],
	['Cubo siamês', [
		['1x1x3 block', "sia113", 25],
		['1x2x3 block', "sia123", 25],
		['2x2x2 block', "sia222", 25]
	]],
	['Square', [
		['Square-2', "sq2", 20],
		['Super Square-1', "ssq1t", 20]
	]],
	['Super Floppy', [
		[' ', "sfl", 25]
	]],
	['UFO', [
		['Jaap style', "ufo", 25]
	]],
	['FTO (Face-Turning Octahedron)', [
		['random state', "ftoso", 0],
		['movimento aleatório', "fto", 30],
		['L3T', "ftol3t", 0],
		['L3T+LBT', "ftol4t", 0],
		['TCP', "ftotcp", 0],
		['edges only', "ftoedge", 0],
		['centers only', "ftocent", 0],
		['corners only', "ftocorn", 0],
		['Diamond random state', "dmdso", 0]
	]],
	['Icosahedron', [
		['Icosamate movimento aleatório', "ctico", 60]
	]],
	['===ESPECIAIS===', [
		['--', "blank", 0]
	]],
	['3x3x3 subsets', [
		['2-generator R,U', "2gen", 0],
		['2-generator L,U', "2genl", 0],
		['Roux-generator M,U', "roux", 0],
		['3-generator F,R,U', "3gen_F", 0],
		['3-generator R,U,L', "3gen_L", 0],
		['3-generator R,r,U', "RrU", 0],
		['Domino Subgroup', "333drud", 0],
		['half turns only', "half", 0],
		['last slot + last layer (old)', "lsll", 15]
	]],
	['Bandaged Cube', [
		['Bicube', "bic", 30],
		['Square-1 /,(1,0)', "bsq", 25]
	]],
	['Relays', [
		['lots of 3x3x3s', "r3", 5],
		['234 relay', "r234", 0],
		['2345 relay', "r2345", 0],
		['23456 relay', "r23456", 0],
		['234567 relay', "r234567", 0],
		['234 relay (WCA)', "r234w", 0],
		['2345 relay (WCA)', "r2345w", 0],
		['23456 relay (WCA)', "r23456w", 0],
		['234567 relay (WCA)', "r234567w", 0],
		['Mini Guildford', "rmngf", 0]
	]],
	['===ZUEIRAS===', [
		['--', "blank", 0]
	]],
	['1x1x1', [
		['x y z', "111", 25]
	]],
	['-1x-1x-1', [
		[' ', "-1", 25]
	]],
	['1x1x2', [
		[' ', "112", 25]
	]],
	['LOL', [
		[' ', "lol", 25]
	]],
	['Derrick Eide', [
		[' ', "eide", 25]
	]]
];
var SCRAMBLE_NOOBST = [
	['gire a face do topo', 'gire a face da base'],
	['gire a face da direita', 'fire a face da esquerda'],
	['gire a face da frente', 'gire a face de trás']
];
var SCRAMBLE_NOOBSS = ' 90 graus no sentido horário,| 90 graus no sentido anti-horário,|  180 graus,';
var SCROPT_TITLE = 'Scramble Options';
var SCROPT_BTNALL = 'Full';
var SCROPT_BTNNONE = 'Clear';
var SCROPT_EMPTYALT = 'Please select at least one case';
var STATS_CFM_RESET = 'apagar todos os tempos desta sessão??????';
var STATS_CFM_DELSS = 'Deletar a sessão [%s]????';
var STATS_CFM_DELMUL = 'Deletar quantas solves a partir do índice atual?';
var STATS_CFM_DELETE = 'deletar este tempo?';
var STATS_COMMENT = 'Comentário';
var STATS_REVIEW = 'Review';
var STATS_DATE = 'Data';
var STATS_SSSTAT = '1-solve stat.';
var STATS_SSRETRY = 'Retry';
var STATS_CURROUND = 'Estatísticas do round atual';
var STATS_CURSESSION = 'Estatísticas atuais da sessão';
var STATS_CURSPLIT = 'Phase %d of Current Session Statistics';
var STATS_EXPORTCSV = 'Exportar CSV';
var STATS_SSMGR_TITLE = 'Session Manager';
var STATS_SSMGR_NAME = 'Nome';
var STATS_SSMGR_DETAIL = 'Session Details';
var STATS_SSMGR_OPS = 'Renomear|Criar|Dividir|Mesclar|Excluir|Ordenar|Merge&Dedupe';
var STATS_SSMGR_ORDER = 'Ordenar por embaralhamento';
var STATS_SSMGR_ODCFM = 'Ordenar todas as sessões por embaralhamento?';
var STATS_SSMGR_SORTCFM = '%d solve(s) will be reordered, confirm?';
var STATS_ALERTMG = 'Mesclar todos os tempos na sessão [%f] até o final da sessão [%t]?';
var STATS_PROMPTSPL = 'Number of latest times split from session [%s]?';
var STATS_ALERTSPL = 'Should split or leave 1 time at least';
var STATS_AVG = 'média';
var STATS_SUM = 'sum';
var STATS_SOLVE = 'resolvendo';
var STATS_TIME = 'tempo';
var STATS_SESSION = 'Sessão';
var STATS_SESSION_NAME = 'Editar nome da sessão';
var STATS_SESSION_NAMEC = 'Nome da nova sessão';
var STATS_STRING = 'melhor|atual|pior|Gerado pelo csTimer em %Y-%M-%D|solves/total: %d|single|média de %mk|média de %mk|Média: %v{ (s = %sgm)}|Média: %v|Lista de Tempos:|solving from %s to %e|Totally spent: %d|target';
var STATS_PREC = 'precisão da distribuição de tempos';
var STATS_PREC_STR = 'automático|0.1s|0.2s|0.5s|1s|2s|5s|10s|20s|50s|100s';
var STATS_TYPELEN = 'tipo da lista %d|tamanho da lista %d|average|média';
var STATS_STATCLR = 'Habilitar o esvaziamento de sessão';
var STATS_ABSIDX = 'Show absolute index in statistics report';
var STATS_XSESSION_DATE = 'qualquer data | últimas 24 horas | últimos 7 dias | últimos 30 dias | últimos 365 dias';
var STATS_XSESSION_NAME = 'qualquer nome';
var STATS_XSESSION_SCR = 'qualquer embaralhamento';
var STATS_XSESSION_CALC = 'Calc';
var STATS_RSFORSS = 'Show stat. when clicking solve number';
var PROPERTY_PRINTSCR = 'mostrar embaralhamentos nas estatísticas';
var PROPERTY_PRINTCOMM = 'print comment(s) in statistics';
var PROPERTY_PRINTDATE = 'mostrar data de resolução nas estatísticas';
var PROPERTY_SUMMARY = 'mostrar dados antes da lista de tempos';
var PROPERTY_IMRENAME = 'renomear a sessão imediatamente depois de criar';
var PROPERTY_SCR2SS = 'criar uma nova sessão quando mudar o tipo de embaralhamento';
var PROPERTY_SS2SCR = 'restaurar o tipo de embaralhamento quando mudar de sessão';
var PROPERTY_SS2PHASES = 'restaurar modadlidade de múltiplas fases quando mudar de sessão';
var PROPERTY_STATINV = 'Lista de tempos ao contrário';
var PROPERTY_STATSSUM = 'Show sum in time list';
var PROPERTY_STATTHRES = 'Show target time for session best';
var PROPERTY_STATBPA = 'Show best possible average (BPA)';
var PROPERTY_STATWPA = 'Show worst possible average (WPA)';
var PROPERTY_STATAL = 'Indicadores estatísticos';
var PROPERTY_STATALU = 'Indicador estatístico personalizado';
var PROPERTY_HLPBS = 'Highlight PBs';
var PROPERTY_HLPBS_STR = 'Dark orange as WCA|As link color|Bolder|None';
var PROPERTY_DELMUL = 'Ativar deleção múltipla';
var PROPERTY_TOOLSFUNC = 'Funções selecionadas';
var PROPERTY_TRIM = 'Número de resoluções retiradas de cada lado';
var PROPERTY_TRIMR = 'Number of solves trimmed at worse side';
var PROPERTY_TRIM_MED = 'Mediana';
var PROPERTY_STKHEAD = 'Use Stackmat Status Information';
var PROPERTY_TOOLPOS = 'Tools panel position';
var PROPERTY_TOOLPOS_STR = 'Bottom|Float|Top';
var PROPERTY_HIDEFULLSOL = 'Show solution progressively';
var PROPERTY_IMPPREV = 'Import non-latest data';
var PROPERTY_AUTOEXP = 'Exportação automática (a cada 100 resoluções)';
var PROPERTY_AUTOEXP_OPT = 'Never|To File|With csTimer ID|With WCA Account|With Google Account|Alert Only';
var PROPERTY_SCRASIZE = 'Auto scramble size';
var MODULE_NAMES = {
	"kernel": 'Global',
	"ui": 'Tela',
	"color": 'Cor',
	"timer": 'Timer',
	"scramble": 'Embaralhamento',
	"stats": 'Estatísticas',
	"tools": 'Ferramentas',
	"vrc": 'virtual&<br>bluetooth'
};
var BGIMAGE_URL = 'por favor, insira o url da imagem';
var BGIMAGE_INVALID = 'url inválido!';
var BGIMAGE_OPACITY = 'opacidade da imagem de fundo';
var BGIMAGE_IMAGE = 'imagem de fundo';
var BGIMAGE_IMAGE_STR = 'nenhum|manual|CCT';
var SHOW_AVG_LABEL = 'Mostrar averages rápidas na frente';
var SHOW_DIFF_LABEL = 'Show Difference Label';
var SHOW_DIFF_LABEL_STR = '-Green+Red|-Red+Green|Normal|None';
var USE_LOGOHINT = 'mensagens de dicas na logo';
var TOOLS_SCRGEN = 'Gerador de Embaralhamentos';
var SCRGEN_NSCR = 'Número de embaralhamentos';
var SCRGEN_PRE = 'prefixo';
var SCRGEN_GEN = 'Gerar embaralhamentos!!';
var VRCREPLAY_TITLE = 'Virtual Replay';
var VRCREPLAY_ORI = 'raw ori|auto ori';
var VRCREPLAY_SHARE = 'share link';
var GIIKER_CONNECT = 'Click to connect';
var GIIKER_RESET = 'Reset (Mark Solved)';
var GIIKER_REQMACMSG = 'Please enter the MAC address of your smart hardware (xx:xx:xx:xx:xx:xx). You can find the MAC address through chrome://bluetooth-internals/#devices, or modify following options to let csTimer automatically obtain it:\nChrome: Turn on chrome://flags/#enable-experimental-web-platform-features\nBluefy: Turn on Enable BLE Advertisements';
var GIIKER_NOBLEMSG = 'Bluetooth API is not available. Ensure https access, check bluetooth is enabled on your device, and try chrome with chrome://flags/#enable-experimental-web-platform-features enabled';
var PROPERTY_SHOWAD = 'Show advertisements (take effect after reload)';
var PROPERTY_GIIORI = 'Cube orientation';
var LGHINT_INVALID = 'Invalid Value!';
var LGHINT_NETERR = 'Network Error!';
var LGHINT_SERVERR = 'Server Error!';
var LGHINT_SUBMITED = 'Submitted';
var LGHINT_SSBEST = 'Session best %s!';
var LGHINT_SCRCOPY = 'Scramble copied';
var LGHINT_LINKCOPY = 'Share link copied';
var LGHINT_SOLVCOPY = 'Solve copied';
var LGHINT_SORT0 = 'Already sorted';
var LGHINT_IMPORTED = 'Import %d session(s)';
var LGHINT_IMPORT0 = 'No session imported';
var LGHINT_BTCONSUC = 'Bluetooth successfully connected';
var LGHINT_BTDISCON = 'Bluetooth disconnected';
var LGHINT_BTNOTSUP = 'Not support your smart cube';
var LGHINT_BTINVMAC = 'Not a valid mac address, cannot connect to your smart cube';
var LGHINT_AEXPABT = 'Auto export abort';
var LGHINT_AEXPSUC = 'Auto export success';
var LGHINT_AEXPFAL = 'Auto export failed';
var EASY_SCRAMBLE_HINT = 'Change length to limit upper bound of solution length, input 2 digits to limit both lower (<= 8) and upper bound';
