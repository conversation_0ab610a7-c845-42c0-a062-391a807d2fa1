{"name": "stackmat", "version": "1.1.1", "description": "Subscribe to events received from a Stackmat timer connected to the browser via the HTML5 Audio API.", "keywords": ["Stackmat", "<PERSON><PERSON><PERSON>'s <PERSON><PERSON>"], "homepage": "https://github.com/stilesdev/stackmat", "bugs": "https://github.com/stilesdev/stackmat/issues", "license": "MIT", "author": {"name": "<PERSON>", "url": "https://github.com/stilesdev"}, "files": ["dist/**/*"], "browser": "dist/module/index.js", "types": "dist/module/index.d.ts", "unpkg": "dist/umd/stackmat.min.js", "scripts": {"build": "npm run build:ts && npm run build:umd && npm run build:umd:min", "build:ts": "tsc", "build:umd": "rollup -c", "build:umd:min": "rollup -c --environment BUILD:minify", "lint": "eslint ./src", "release": "bumpp package.json", "watch": "concurrently npm:watch:ts npm:watch:umd", "watch:ts": "tsc --watch", "watch:umd": "rollup -c --watch"}, "devDependencies": {"@rollup/plugin-terser": "^0.1.0", "@rollup/plugin-typescript": "^9.0.2", "@stilesdev/eslint-config-compat": "^0.3.0", "@stilesdev/eslint-config-typescript": "^0.3.0", "bumpp": "^8.2.1", "concurrently": "^7.5.0", "eslint": "^8.27.0", "rollup": "^2.79.1", "typescript": "^4.8.4"}}