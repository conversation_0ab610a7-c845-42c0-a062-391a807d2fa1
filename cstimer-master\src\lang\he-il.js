var OK_LANG = 'או קיי';
var CANCEL_LANG = 'ביטול';
var RESET_LANG = 'אתחל';
var ABOUT_LANG = 'אודות';
var ZOOM_LANG = 'שנה מרחק מתצוגה';
var COPY_LANG = 'העתק';
var BUTTON_TIME_LIST = 'הצג<br>זמנים';
var BUTTON_OPTIONS = 'אפשרויות';
var BUTTON_EXPORT = 'ייצוא';
var BUTTON_DONATE = 'תרום';
var PROPERTY_SR = 'עם סשן';
var PROPERTY_USEINS = 'השתמש בסקירת WCA';
var PROPERTY_USEINS_STR = 'תמיד (למטה)|תמיד (למעלה)|חוץ מבעיניים עצומות(למטה)|חוץ מבעיניים עצומות(למעלה)|אף פעם';
var PROPERTY_SHOWINS = 'Show an icon when inspection is enabled';
var PROPERTY_VOICEINS = 'התראה קולית של סקירת WCA';
var PROPERTY_VOICEINS_STR = 'ללא|קול זכר|קול נקבה';
var PROPERTY_VOICEVOL = 'עוצמת שמע';
var PROPERTY_PHASES = 'רב-שלבי';
var PROPERTY_TIMERSIZE = 'גודל טיימר';
var PROPERTY_USEMILLI = 'השתמש באלפיות שנייה';
var PROPERTY_SMALLADP = 'השתמש בגופן קטן אחרי נקודה עשרונית';
var PROPERTY_SCRSIZE = 'גודל בלגון';
var PROPERTY_SCRMONO = 'רווח יחיד בבלגון';
var PROPERTY_SCRLIM = 'הגבל את גובה אזור הבלגון';
var PROPERTY_SCRALIGN = 'יישור אזור הבלגון';
var PROPERTY_SCRALIGN_STR = 'מרכז|שמאל|ימין';
var PROPERTY_SCRWRAP = 'Scramble Wrap';
var PROPERTY_SCRWRAP_STR = 'Balanced|Normal';
var PROPERTY_SCRNEUT = 'Color neutral';
var PROPERTY_SCRNEUT_STR = 'None|Single face|Double faces|Six faces';
var PROPERTY_SCREQPR = 'Probabilities for training-scramble states';
var PROPERTY_SCREQPR_STR = 'Actual|Equal|Random order';
var PROPERTY_SCRFAST = 'משתמש בבלגון מהיר ל-4×4×4 (לא רשמי)';
var PROPERTY_SCRKEYM = 'סמן מהלך(י) מפתח בבלגון';
var PROPERTY_SCRCLK = 'Action when clicking scramble';
var PROPERTY_SCRCLK_STR = 'None|Copy|Next scramble';
var PROPERTY_WNDSCR = 'סגנון תצוגה של לוח הבלגון';
var PROPERTY_WNDSTAT = 'סגנון תצוגת לוח סטטיסטיקות';
var PROPERTY_WNDTOOL = 'סגנון תצוגת לוח כלים';
var PROPERTY_WND_STR = 'רגיל|שטוח';
var EXPORT_DATAEXPORT = 'ייבוא/ייצוא נתונים';
var EXPORT_TOFILE = 'ייצא לקובץ';
var EXPORT_FROMFILE = 'ייבא מקובץ';
var EXPORT_TOSERV = 'ייצוא לשרת';
var EXPORT_FROMSERV = 'ייבוא משרת';
var EXPORT_FROMOTHER = 'ייבוא רשימת פתירות מטיימרים אחרים';
var EXPORT_USERID = 'אנא הזן את חשבונך (רק אותיות ו/או מספרים)';
var EXPORT_INVID = 'ניתן להזין רק אותיות ו/או מספרים!';
var EXPORT_ERROR = 'אירעו מספר שגיאות...';
var EXPORT_NODATA = 'לא נמצאו נתונים עבור החשבון שלך';
var EXPORT_UPLOADED = 'הקובץ הועלה בהצלחה';
var EXPORT_CODEPROMPT = 'Save this code, or type saved code to import';
var EXPORT_ONLYOPT = 'Export/Import only Options';
var EXPORT_ACCOUNT = 'יצא משתמשים';
var EXPORT_LOGINGGL = 'התחבר עם חשבון גוגל';
var EXPORT_LOGINWCA = 'התחבר עם חשבון WCA';
var EXPORT_LOGOUTCFM = 'האם להתנתק?';
var EXPORT_LOGINAUTHED = 'Authorized<br>Fetching Data...';
var EXPORT_AEXPALERT = 'More than %d solves since last backup';
var EXPORT_WHICH = 'You have %d file(s), which one should be imported?';
var EXPORT_WHICH_ITEM = '%s solve(s), uploaded at %t';
var IMPORT_FINAL_CONFIRM = 'This will override all local data! It will modify %d sessions, add %a and remove %r solves at least. Confirm to import data?';
var BUTTON_SCRAMBLE = 'בלגון';
var BUTTON_TOOLS = 'כלים';
var IMAGE_UNAVAILABLE = 'לא ניתן להציג עבור סוג בלגון זה';
var TOOLS_SELECTFUNC = 'פונקציה';
var TOOLS_CROSS = 'פתור פלוס';
var TOOLS_EOLINE = 'פתור EOLine';
var TOOLS_ROUX1 = 'פתור Roux S1';
var TOOLS_222FACE = 'פאת 2x2x2';
var TOOLS_GIIKER = 'קוביית Giiker';
var TOOLS_IMAGE = 'בלגון חדש';
var TOOLS_STATS = 'סטטיסטיקות';
var TOOLS_HUGESTATS = 'סטטיסטיקות רשימת פלוס';
var TOOLS_DISTRIBUTION = 'חלוקת זמן';
var TOOLS_TREND = 'מגמת זמן';
var TOOLS_METRONOME = 'מטרונום';
var TOOLS_RECONS = 'בנה מחדש';
var TOOLS_RECONS_NODATA = 'No solution found.';
var TOOLS_RECONS_TITLE = 'insp|exec|turn|tps';
var TOOLS_TRAINSTAT = 'Training Stat.';
var TOOLS_BLDHELPER = 'BLD Helper';
var TOOLS_CFMTIME = 'אשר זמן';
var TOOLS_SOLVERS = 'פותרים';
var TOOLS_DLYSTAT = 'Daily Statistics';
var TOOLS_DLYSTAT1 = 'Period|Start of Day|Week';
var TOOLS_DLYSTAT_OPT1 = 'day|week|month|year';
var TOOLS_DLYSTAT_OPT2 = 'Sun|Mon|Tue|Wed|Thu|Fri|Sat';
var TOOLS_SYNCSEED = 'ערבוב נפוץ';
var TOOLS_SYNCSEED_SEED = 'זרע';
var TOOLS_SYNCSEED_INPUT = 'זרע קלט';
var TOOLS_SYNCSEED_30S = 'השתמש בזרע חדש כל 30ש';
var TOOLS_SYNCSEED_HELP = 'If enabled, scramble will only depend on the seed and scramble settings.';
var TOOLS_SYNCSEED_DISABLE = 'כבה זרע נוכחי?';
var TOOLS_SYNCSEED_INPUTA = 'הכנס ערך (a-zA-Z0-9) כזרע';
var TOOLS_BATTLE = 'Online battle';
var TOOLS_BATTLE_HEAD = 'Room|Join Room';
var TOOLS_BATTLE_TITLE = 'Rank|Status|Time';
var TOOLS_BATTLE_STATUS = 'Ready|Inspect|Solving|Solved|Lost';
var TOOLS_BATTLE_INFO = 'Join a battle room with your friend, then you will battle together.';
var TOOLS_BATTLE_JOINALERT = 'Please input the room ID';
var TOOLS_BATTLE_LEAVEALERT = 'Leave current room';
var OLCOMP_UPDATELIST = 'עדכן רשימת תחרויות';
var OLCOMP_VIEWRESULT = 'צפה בתוצאה';
var OLCOMP_VIEWMYRESULT = 'My History';
var OLCOMP_START = 'התחל!';
var OLCOMP_SUBMIT = 'הגש!';
var OLCOMP_SUBMITAS = 'הגש כ:';
var OLCOMP_WCANOTICE = 'הגש עם משתמש WCA? (התחבר מחדש אם אתה לא מזוהה אחרי שאתה מגיש)';
var OLCOMP_OLCOMP = 'תחרות אונליין';
var OLCOMP_ANONYM = 'אנונימי';
var OLCOMP_ME = 'אני';
var OLCOMP_WCAACCOUNT = 'משתמש WCA';
var OLCOMP_ABORT = 'Abort competition and show results?';
var OLCOMP_WITHANONYM = 'With Anonym';
var PROPERTY_IMGSIZE = 'גודל תמונת בלגון';
var PROPERTY_IMGREP = 'Show virtual cube animation when clicking scramble image';
var TIMER_INSPECT = 'סקור';
var TIMER_SOLVE = 'פתור';
var PROPERTY_USEMOUSE = 'שימוש בטיימר עם העכבר';
var PROPERTY_TIMEU = 'עדכון הטיימר הוא';
var PROPERTY_TIMEU_STR = 'עדכון|0.1ש|שניות|סקירה|ללא';
var PROPERTY_PRETIME = 'זמן השארת מקש הרווח לחוץ(שנייה(ות))';
var PROPERTY_ENTERING = 'הזן זמנים עם';
var PROPERTY_ENTERING_STR = 'טיימר|הקלדה|Stackmat|טיימרמויו|וירטואלי|Bluetooth|qCube|GanTimer|last layer training';
var PROPERTY_INTUNIT = 'יחידה כשמכניסים מספר';
var PROPERTY_INTUNIT_STR = 'שנייה|עשירית שנייה|מאית שנייה';
var PROPERTY_COLOR = 'בחר צבע נושא';
var PROPERTY_COLORS = 'צבע פונט|צבע רקע|צבע לוח|צבע כפתור|צבע קישור|צבע לוגו|צבע רקע ללוגו';
var PROPERTY_VIEW = 'סגנון ממשק משתמש הוא';
var PROPERTY_VIEW_STR = 'אוטומטי | מובייל | מחשב שולחני';
var PROPERTY_UIDESIGN = 'UI design is';
var PROPERTY_UIDESIGN_STR = 'רגיל|עיצוב חומר|רגיל בלי צל|עיצוב חומר בלי צל';
var COLOR_EXPORT = 'אנא שמור את המחרוזת לייבוא';
var COLOR_IMPORT = 'בבקשה תכניס את הטקסט המיוצא';
var COLOR_FAIL = 'מידע שגוי, ייבוא נכשל';
var PROPERTY_FONTCOLOR_STR = 'שחור|לבן';
var PROPERTY_COLOR_STR = 'ידני|יבוא/יצוא...|רנדומלי|סטייל1|סטייל2|סטייל3|שחור|לב|סטייל6|חושך מואר| אור מוחשך';
var PROPERTY_FONT = 'תבחר גופן לטיימר';
var PROPERTY_FONT_STR = 'דיגיטלי רנדומלי|רגיל|דיגיטלי1|דיגיטלי2|דיגיטלי3|דיגיטלי4|דיגיטלי5';
var PROPERTY_FORMAT = 'פורמט זמן';
var PROPERTY_USEKSC = 'השתמש בקיצורי מקלדת';
var PROPERTY_USEGES = 'use gesture control';
var PROPERTY_NTOOLS = 'מספר כלים';
var PROPERTY_AHIDE = 'החבא את כל האלמנטים כאשר מודד זמן';
var SCRAMBLE_LAST = 'האחרון';
var SCRAMBLE_NEXT = 'הבא';
var SCRAMBLE_SCRAMBLE = 'בלגון';
var SCRAMBLE_SCRAMBLING = 'Scrambling';
var SCRAMBLE_LENGTH = 'אורך';
var SCRAMBLE_INPUT = 'הקלד בלגון(ים)';
var SCRAMBLE_INPUTTYPE = 'Scramble type';
var PROPERTY_VRCSPEED = 'VRC base speed (tps)';
var PROPERTY_VRCORI = 'Virtual cube orientation';
var PROPERTY_VRCMP = 'רב-שלבי';
var PROPERTY_VRCMPS = 'ללא|CFOP|CF+OP|CFFFFOP|CFFFFOOPP|Roux';
var PROPERTY_GIIKERVRC = 'הראה קובייה דיגיטלית';
var PROPERTY_GIISOK_DELAY = 'Mark scrambled if stay';
var PROPERTY_GIISOK_DELAYS = '2ש|3ש|4ש|5ש|אף פעם|מבולגן נכון';
var PROPERTY_GIISOK_KEY = 'סמן מבולגן עם רווח';
var PROPERTY_GIISOK_MOVE = 'Mark scrambled by doing';
var PROPERTY_GIISOK_MOVES = 'U4, R4 וכדומה|2(\'U U), 2(U\' U)2 וכדומה|אף פעם';
var PROPERTY_GIISBEEP = 'צפצף כאשר מסומן שמבולגן';
var PROPERTY_GIIRST = 'אפס קוביית בלוטות\' כאשר מחובר';
var PROPERTY_GIIRSTS = 'תמיד|שאל|אף פעם';
var PROPERTY_GIIMODE = 'Bluetooth Cube Mode';
var PROPERTY_GIIMODES = 'Normal|Training|Continuous training';
var PROPERTY_VRCAH = 'Useless pieces in huge cube';
var PROPERTY_VRCAHS = 'Hide|Border|Color|Show';
var CONFIRM_GIIRST = 'אפס קוביית בלוטות\' כאשר נפתר?';
var PROPERTY_GIIAED = 'זיהוי תקלות חומרה אוטומטי';
var scrdata = [
	['WCA', [
		['3×3×3', "333", 0],
		['2×2×2', "222so", 0],
		['4×4×4', "444wca", -40],
		['5×5×5', "555wca", -60],
		['6×6×6', "666wca", -80],
		['7×7×7', "777wca", -100],
		['3×3 בליינד', "333ni", 0],
		['3×3 הכי פחות מהלכים', "333fm", 0],
		['3×3 יד אחת', "333oh", 0],
		['שָׁעוֹן', "clkwca", 0],
		['מגמינקס', "mgmp", -70],
		['פיראמינקס', "pyrso", -10],
		['סקיוב', "skbso", 0],
		['סקוואן', "sqrs", 0],
		['4×4 בליינד', "444bld", -40],
		['5×5 בליינד', "555bld", -60],
		['3×3 מ-בליינד', "r3ni", 5]
	]],
	['קלט', [
		['חיצוני', "input", 0],
		['Competition', "remoteComp", 0],
		['Online battle', "remoteBattle", 0],
		['Remote', "remoteOther", 0]
	]],
	['===WCA===', [
		['--', "blank", 0]
	]],
	['3×3×3', [
		["random state (WCA)", "333", 0],
		['random move', "333o", 25],
		['3×3×3 לנוּבִּים', "333noob", 25],
		['אדג\'ים בלבד', "edges", 0],
		['פינות בלבד', "corners", 0],
		['BLD Helper', "nocache_333bldspec", 0],
		['Pattern Tool', "nocache_333patspec", 0],
		['3×3 רגליים', "333ft", 0],
		['Custom', "333custom", 0]
	]],
	['3×3×3 CFOP', [
		['PLL', "pll", 0],
		['OLL', "oll", 0],
		['סלוט אחרון + שכבה אחרונה', "lsll2", 0],
		['שכבה אחרונה', "ll", 0],
		['ZBLL', "zbll", 0],
		['COLL', "coll", 0],
		['CLL', "cll", 0],
		['ELL', "ell", 0],
		['2GLL', "2gll", 0],
		['ZZLL', "zzll", 0],
		['ZBLS', "zbls", 0],
		['EOLS', "eols", 0],
		['WVLS', "wvls", 0],
		['VLS', "vls", 0],
		['פלוס פתור', "f2l", 0],
		['EOLine', "eoline", 0],
		['EO Cross', "eocross", 0],
		['פלוס קל', "easyc", 3],
		['easy xcross', "easyxc", 4]
	]],
	['3×3×3 Roux', [
		['2nd Block', "sbrx", 0],
		['CMLL', "cmll", 0],
		['LSE', "lse", 0],
		['LSE &lt;M, U&gt;', "lsemu", 0]
	]],
	['3×3×3 Mehta', [
		['3QB', "mt3qb", 0],
		['EOLE', "mteole", 0],
		['TDR', "mttdr", 0],
		['6CP', "mt6cp", 0],
		['CDRLL', "mtcdrll", 0],
		['L5EP', "mtl5ep", 0],
		['TTLL', "ttll", 0]
	]],
	['2×2×2', [
		["random state (WCA)", "222so", 0],
		['optimal', "222o", 0],
		['3-gen', "2223", 25],
		['EG', "222eg", 0],
		['CLL', "222eg0", 0],
		['EG1', "222eg1", 0],
		['EG2', "222eg2", 0],
		['TCLL+', "222tcp", 0],
		['TCLL-', "222tcn", 0],
		['TCLL', "222tc", 0],
		['LS', "222lsall", 0],
		['בלי בלוקים', "222nb", 0]
	]],
	['4×4×4', [
		["WCA", "444wca", -40],
		['מהלך אקראי', "444m", 40],
		['SiGN', "444", 40],
		['YJ', "444yj", 40],
		['אדג\'ים 4×4×4', "4edge", 0],
		['R, r, U, u', "RrUu", 40],
		['Last layer', "444ll", 0],
		['ELL', "444ell", 0],
		['Edge only', "444edo", 0],
		['Center only', "444cto", 0]
	]],
	['4×4×4 Yau/Hoya', [
		['UD center solved', "444ctud", 0],
		['UD+3E solved', "444ud3c", 0],
		['Last 8 dedges', "444l8e", 0],
		['RL center solved', "444ctrl", 0],
		['RLDX center solved', "444rlda", 0],
		['RLDX cross solved', "444rlca", 0]
	]],
	['5×5×5', [
		["WCA", "555wca", 60],
		['SiGN', "555", 60],
		['אדג\'ים 5×5×5', "5edge", 8]
	]],
	['6×6×6', [
		["WCA", "666wca", 80],
		['SiGN', "666si", 80],
		['פרפיקס', "666p", 80],
		['suffix', "666s", 80],
		['אדג\'ים 6×6×6', "6edge", 8]
	]],
	['7×7×7', [
		["WCA", "777wca", 100],
		['SiGN', "777si", 100],
		['prefix', "777p", 100],
		['suffix', "777s", 100],
		['אדג\'ים 7×7×7', "7edge", 8]
	]],
	['שעון', [
		['WCA', "clkwca", 0],
		['רשמי (old)', "clkwcab", 0],
		['WCA w/o y2', "clknf", 0],
		['jaap', "clk", 0],
		['optimal', "clko", 0],
		['קצר', "clkc", 0],
		['efficient pin order', "clke", 0]
	]],
	['מגהמינקס', [
		["WCA", "mgmp", 70],
		['גזר', "mgmc", 70],
		['סגנון ישן', "mgmo", 70],
		['2-ג\'ן R,U', "minx2g", 30],
		['סלוט אחרון + שכבה אחרונה', "mlsll", 0],
		['PLL', "mgmpll", 0],
		['Last Layer', "mgmll", 0]
	]],
	['פיראמינקס', [
		["random state (WCA)", "pyrso", 10],
		['optimal', "pyro", 0],
		['random move', "pyrm", 25],
		['L4E', "pyrl4e", 0],
		['4 tips', "pyr4c", 0],
		['No bar', "pyrnb", 0]
	]],
	['סקיוב', [
		["random state (WCA)", "skbso", 0],
		['optimal', "skbo", 0],
		['random move', "skb", 25],
		['No bar', "skbnb", 0]
	]],
	['Square-1', [
		["random state (WCA)", "sqrs", 0],
		["CSP", "sqrcsp", 0],
		["PLL", "sq1pll", 0],
		['מטריקת סיבוב פאה', "sq1h", 40],
		['מטריקת סיבוב', "sq1t", 20]
	]],
	['===אחר===', [
		['--', "blank", 0]
	]],
	['פאזל 15', [
		['random state URLD', "15prp", 0],
		['random state ^<>v', "15prap", 0],
		['random state Blank', "15prmp", 0],
		['random move URLD', "15p", 80],
		['random move ^<>v', "15pat", 80],
		['random move Blank', "15pm", 80]
	]],
	['8 puzzle', [
		['random state URLD', "8prp", 0],
		['random state ^<>v', "8prap", 0],
		['random state Blank', "8prmp", 0]
	]],
	['LxMxN', [
		['1x3x3', "133", 0],
		['2x2x3', "223", 0],
		['2x3x3', "233", 25],
		['3x3x4', "334", 40],
		['3x3x5', "335", 25],
		['3x3x6', "336", 40],
		['3x3x7', "337", 40],
		['8x8x8', "888", 120],
		['9x9x9', "999", 120],
		['10x10x10', "101010", 120],
		['11x11x11', "111111", 120],
		['NxNxN', "cubennn", 12]
	]],
	['קוביית גלגלי שיניים (גיר)', [
		['random state', "gearso", 0],
		['optimal', "gearo", 0],
		['random move', "gear", 10]
	]],
	['Kilominx', [
		['random state', "klmso", 0],
		['Pochmann', "klmp", 30]
	]],
	['גיגמינקס', [
		['פוקמן', "giga", 300]
	]],
	['Crazy Puzzle', [
		['Crazy 3x3x3', "crz3a", 30]
	]],
	['Cmetrick', [
		['Cmetrick', "cm3", 25],
		['Cmetrick Mini', "cm2", 25]
	]],
	['קוביית הליקופטר', [
		['Heli copter', "heli", 40],
		['Curvy copter', "helicv", 40],
		['2x2 Heli random move', "heli2x2", 70],
		['2x2 Heli by group', "heli2x2g", 5]
	]],
	['קוביית רדי', [
		['random state', "rediso", 0],
		['MoYu', "redim", 8],
		['random move', "redi", 20]
	]],
	['Dino Cube', [
		['random state', "dinoso", 0],
		['optimal', "dinoo", 0]
	]],
	['Ivy cube', [
		['random state', "ivyso", 0],
		['optimal', "ivyo", 0],
		['random move', "ivy", 10]
	]],
	['Master Pyraminx', [
		['random state', "mpyrso", 0],
		['random move', "mpyr", 42]
	]],
	['פיראמינקס קריסטל', [
		['פוקמן', "prcp", 70],
		['סגנון ישן', "prco", 70]
	]],
	['קובייה סיאמית', [
		['בלוק 1×1×3', "sia113", 25],
		['בלוק 1×2×3', "sia123", 25],
		['בלוק 2×2×2', "sia222", 25]
	]],
	['Square', [
		['Square-2', "sq2", 20],
		['סופר סקוואן', "ssq1t", 20]
	]],
	['Super Floppy', [
		[' ', "sfl", 25]
	]],
	['עב"מ', [
		['Jaap style', "ufo", 25]
	]],
	['אוקטהדרון פאות', [
		['random state', "ftoso", 0],
		['random move', "fto", 30],
		['L3T', "ftol3t", 0],
		['L3T+LBT', "ftol4t", 0],
		['TCP', "ftotcp", 0],
		['edges only', "ftoedge", 0],
		['centers only', "ftocent", 0],
		['corners only', "ftocorn", 0],
		['Diamond random state', "dmdso", 0]
	]],
	['Icosahedron', [
		['Icosamate random move', "ctico", 60]
	]],
	['===מיוחד===', [
		['--', "blank", 0]
	]],
	['קבוצות משנה 3×3×3', [
		['2-ג\'ן R,U', "2gen", 0],
		['2-ג\'ן L,U', "2genl", 0],
		['רוּ ג\'ן M,U', "roux", 0],
		['3-ג\'ן F,R,U', "3gen_F", 0],
		['3-ג\'ן R,U,L', "3gen_L", 0],
		['3-ג\'ן R,r,U', "RrU", 0],
		['Domino Subgroup', "333drud", 0],
		['חצאי מהלכים בלבד', "half", 0],
		['סלוט אחרון + שכבה אחרונה (ישן)', "lsll", 15]
	]],
	['Bandaged Cube', [
		['Bicube', "bic", 30],
		['Square-1 /,(1,0)', "bsq", 25]
	]],
	['קוביות מרובות', [
		['הרבה 3x3x3', "r3", 5],
		['234', "r234", 0],
		['2345', "r2345", 0],
		['23456', "r23456", 0],
		['2 - 7', "r234567", 0],
		['234 (WCA)', "r234w", 0],
		['2345 (WCA)', "r2345w", 0],
		['23456 (WCA)', "r23456w", 0],
		['2 - 7 (WCA)', "r234567w", 0],
		['Mini Guildford', "rmngf", 0]
	]],
	['===בדיחות===', [
		['--', "blank", 0]
	]],
	['1x1x1', [
		['x y z', "111", 25]
	]],
	['-1x-1x-1', [
		[' ', "-1", 25]
	]],
	['1x1x2', [
		[' ', "112", 25]
	]],
	['LOL', [
		[' ', "lol", 25]
	]],
	['Derrick Eide', [
		[' ', "eide", 25]
	]]
];
var SCRAMBLE_NOOBST = [
	['סובב את הפאה העליונה', 'סובב את הפאה התחתונה'],
	['סובב את הפאה הימנית', 'סובב את הפאה השמאלית'],
	['סובב את הפאה הקדמית', 'סובב את הפאה האחורית']
];
var SCRAMBLE_NOOBSS = 'בכיוון השעון 90°,| נגד כיוון השעון 90°,| ב-180°';
var SCROPT_TITLE = 'Scramble Options';
var SCROPT_BTNALL = 'Full';
var SCROPT_BTNNONE = 'Clear';
var SCROPT_EMPTYALT = 'בחר לפחות מקרה אחד בבקשה';
var STATS_CFM_RESET = 'אפס את כל הזמנית בסשן הזה?';
var STATS_CFM_DELSS = 'מחק סשן [%s]?';
var STATS_CFM_DELMUL = 'כמות הערכים המחוקים מהאינדקס הנוכחי?';
var STATS_CFM_DELETE = 'למחוק את הזמן הזה?';
var STATS_COMMENT = 'הערה';
var STATS_REVIEW = 'ביקורת';
var STATS_DATE = 'תאריך';
var STATS_SSSTAT = 'סטטיסטיקות פתירה אחת';
var STATS_SSRETRY = 'Retry';
var STATS_CURROUND = 'נתוני סיבוב נוכחי';
var STATS_CURSESSION = 'נתוני סשן נוכחי';
var STATS_CURSPLIT = 'שלב %d של סטטיסטיקות הסשן הנוכחי';
var STATS_EXPORTCSV = 'יצא CSV';
var STATS_SSMGR_TITLE = 'מנהל הסשנים';
var STATS_SSMGR_NAME = 'שם';
var STATS_SSMGR_DETAIL = 'פרטי סשן';
var STATS_SSMGR_OPS = 'שנה שם|הכן|פצל|שלב|מחק|סדר|Merge&Dedupe';
var STATS_SSMGR_ORDER = 'סדר לפי בלגון';
var STATS_SSMGR_ODCFM = 'סדר את כל הסשנים לפי בלגון?';
var STATS_SSMGR_SORTCFM = '%d solve(s) will be reordered, confirm?';
var STATS_ALERTMG = 'שלב את כל השמנים בסשן [%f] לסוף של סשן [%t]?';
var STATS_PROMPTSPL = 'כמות הזמנים האחרונים הנחתכים מסשן [%s]?';
var STATS_ALERTSPL = 'לחתוך או להשאיר לפחות זמן אחד?';
var STATS_AVG = 'ממוצע';
var STATS_SUM = 'sum';
var STATS_SOLVE = 'פתירה';
var STATS_TIME = 'זמן';
var STATS_SESSION = 'סשן';
var STATS_SESSION_NAME = 'ערוך שם סשן';
var STATS_SESSION_NAMEC = 'שם הסשן החדש';
var STATS_STRING = 'הכי טוב|עכשווי|הכי גרוע|יוצר על ידי csTimer ב%Y-%M-%D|פתירות/סך הכל: %d|סינגל|ממוצע של %mk| ממוצע של %mk| ממוצע: Average: %v{ (σ = %sgm)}|ממוצע: %v|רשימת זמנים:|פתירות מ%s עד %e|זמן סך הכל: %d|target';
var STATS_PREC = 'דיוק פריסת זמנים';
var STATS_PREC_STR = 'אוטומטי|0.1ש|0.2ש|0.5ש|1ש|2ש|5ש|10ש|20ש|50ש|100ש';
var STATS_TYPELEN = 'רשימה %d סוג| רשימה %d אורך|ממוצע (בלי צדדים)|ממוצע';
var STATS_STATCLR = 'להפעיל ריקון סשנים';
var STATS_ABSIDX = 'Show absolute index in statistics report';
var STATS_XSESSION_DATE = 'כל תאריך|24 השעות האחרונות|ה-7 ימים האחרונים|החודש האחרון|השנה האחרונה';
var STATS_XSESSION_NAME = 'כל שם';
var STATS_XSESSION_SCR = 'כל ערבוב';
var STATS_XSESSION_CALC = 'חשב';
var STATS_RSFORSS = 'הראה סטטיסטיקות כשלוחצים על מספר פתירה';
var PROPERTY_PRINTSCR = 'הראה ערבוב(ים) בסטטיסטיקות';
var PROPERTY_PRINTCOMM = 'print comment(s) in statistics';
var PROPERTY_PRINTDATE = 'הראה תאריך בסטטיסטיקות';
var PROPERTY_SUMMARY = 'הראה סיכום לפני רשימת זמנים';
var PROPERTY_IMRENAME = 'שנה שם סשן ישר אחרי יצירה';
var PROPERTY_SCR2SS = 'תיצור סשן חדש כשמחליפים סוג ערבוב';
var PROPERTY_SS2SCR = 'לשמר סוג ערבוב כשמחליפים סשן';
var PROPERTY_SS2PHASES = 'restore multi-phase timing when switching session';
var PROPERTY_STATINV = 'להפוך רשימת זמנים';
var PROPERTY_STATSSUM = 'Show sum in time list';
var PROPERTY_STATTHRES = 'Show target time for session best';
var PROPERTY_STATBPA = 'Show best possible average (BPA)';
var PROPERTY_STATWPA = 'Show worst possible average (WPA)';
var PROPERTY_STATAL = 'אינדקטורים סטטיסטיים';
var PROPERTY_STATALU = 'אינדקטור סטטיסטי מותאם אישית';
var PROPERTY_HLPBS = 'Highlight PBs';
var PROPERTY_HLPBS_STR = 'Dark orange as WCA|As link color|Bolder|None';
var PROPERTY_DELMUL = 'להפעיל מחיקה מרובה';
var PROPERTY_TOOLSFUNC = 'הפונקציות שנבחרו';
var PROPERTY_TRIM = 'כמות פתירות חתוכה מכל צד';
var PROPERTY_TRIMR = 'Number of solves trimmed at worse side';
var PROPERTY_TRIM_MED = 'חציון';
var PROPERTY_STKHEAD = 'Use Stackmat Status Information';
var PROPERTY_TOOLPOS = 'Tools panel position';
var PROPERTY_TOOLPOS_STR = 'Bottom|Float|Top';
var PROPERTY_HIDEFULLSOL = 'Show solution progressively';
var PROPERTY_IMPPREV = 'יבא מידע לא עדכני';
var PROPERTY_AUTOEXP = 'יצוא אוטומטי (כל 100 פתירות)';
var PROPERTY_AUTOEXP_OPT = 'אף פעם|לקובץ|עם משתמש csTimer|עם משתמש WCA|עם משתמש Google|Alert Only';
var PROPERTY_SCRASIZE = 'גודל ערבוב אוטומטי';
var MODULE_NAMES = {
	"kernel": 'גלובלי',
	"ui": 'תצוגה',
	"color": 'צבע',
	"timer": 'טיימר',
	"scramble": 'בלגון',
	"stats": 'סטטיסטיקות',
	"tools": 'כלים',
	"vrc": 'virtual&<br>bluetooth'
};
var BGIMAGE_URL = 'בבקשה תכניס את הurl של התמונה';
var BGIMAGE_INVALID = 'כתובת אתר לא חוקית';
var BGIMAGE_OPACITY = 'שקיפות תמונת רקע';
var BGIMAGE_IMAGE = 'תמונת רקע';
var BGIMAGE_IMAGE_STR = 'כלום|ידני|CCT';
var SHOW_AVG_LABEL = 'הראה ממוצע מתחת לטיימר';
var SHOW_DIFF_LABEL = 'Show Difference Label';
var SHOW_DIFF_LABEL_STR = '-Green+Red|-Red+Green|Normal|None';
var USE_LOGOHINT = 'Hint messages in logo';
var TOOLS_SCRGEN = 'מייצר ערבובים';
var SCRGEN_NSCR = 'כמות ערבובים';
var SCRGEN_PRE = 'קידומת';
var SCRGEN_GEN = 'יצר ערבובים';
var VRCREPLAY_TITLE = 'Virtual Replay';
var VRCREPLAY_ORI = 'raw ori|auto ori';
var VRCREPLAY_SHARE = 'share link';
var GIIKER_CONNECT = 'Click to connect';
var GIIKER_RESET = 'Reset (Mark Solved)';
var GIIKER_REQMACMSG = 'Please enter the MAC address of your smart hardware (xx:xx:xx:xx:xx:xx). You can find the MAC address through chrome://bluetooth-internals/#devices, or modify following options to let csTimer automatically obtain it:\nChrome: Turn on chrome://flags/#enable-experimental-web-platform-features\nBluefy: Turn on Enable BLE Advertisements';
var GIIKER_NOBLEMSG = 'Bluetooth API is not available. Ensure https access, check bluetooth is enabled on your device, and try chrome with chrome://flags/#enable-experimental-web-platform-features enabled';
var PROPERTY_SHOWAD = 'Show advertisements (take effect after reload)';
var PROPERTY_GIIORI = 'Cube orientation';
var LGHINT_INVALID = 'Invalid Value!';
var LGHINT_NETERR = 'Network Error!';
var LGHINT_SERVERR = 'Server Error!';
var LGHINT_SUBMITED = 'Submitted';
var LGHINT_SSBEST = 'Session best %s!';
var LGHINT_SCRCOPY = 'Scramble copied';
var LGHINT_LINKCOPY = 'Share link copied';
var LGHINT_SOLVCOPY = 'Solve copied';
var LGHINT_SORT0 = 'Already sorted';
var LGHINT_IMPORTED = 'Import %d session(s)';
var LGHINT_IMPORT0 = 'No session imported';
var LGHINT_BTCONSUC = 'Bluetooth successfully connected';
var LGHINT_BTDISCON = 'Bluetooth disconnected';
var LGHINT_BTNOTSUP = 'Not support your smart cube';
var LGHINT_BTINVMAC = 'Not a valid mac address, cannot connect to your smart cube';
var LGHINT_AEXPABT = 'Auto export abort';
var LGHINT_AEXPSUC = 'Auto export success';
var LGHINT_AEXPFAL = 'Auto export failed';
var EASY_SCRAMBLE_HINT = 'Change length to limit upper bound of solution length, input 2 digits to limit both lower (<= 8) and upper bound';
