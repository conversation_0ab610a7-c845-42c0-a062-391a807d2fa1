# scTimer 🧩

A modern, feature-rich speedcubing timer web application with comprehensive puzzle support, advanced statistics, and Stackmat timer integration.

## ✨ Features

### 🎯 Timer Modes

- **Normal Timer**: Classic spacebar-controlled timing with millisecond precision
- **Typing Mode**: Input times manually for offline solves
- **Stackmat Integration**: Professional timer support (desktop only)

### 🧩 Puzzle Support

Complete WCA event coverage with official scrambles:

- **2x2x2 - 7x7x7 Cubes** with official WCA scrambles
- **3x3x3 Variants**: Standard, One-Handed, Blindfolded, Fewest Moves
- **Other WCA Events**: Pyraminx, Skewb, Square-1, Clock, Megaminx
- **Multi-Blind**: Customizable attempt settings with multiple scrambles
- **2D Puzzle Visualization**: Visual representation for all supported puzzles

### 📊 Advanced Statistics

- **Real-time Averages**: ao5, ao12, ao50, ao100 with automatic calculation
- **Detailed Analytics**: Best times, session statistics, solve history
- **Performance Tracking**: Time trends and improvement analysis
- **Session Management**: Separate statistics per puzzle type

### 🌍 Multilingual Support

- **15+ Languages**: English, Spanish, French, German, Chinese, Japanese, and more
- **RTL Support**: Arabic, Hebrew, Kurdish Sorani with proper text direction
- **Dynamic Loading**: Language packs loaded on demand for performance

### 📱 Progressive Web App

- **Installable**: Add to home screen on mobile devices
- **Offline Support**: Works completely without internet connection
- **Responsive Design**: Optimized for all screen sizes and orientations
- **Touch Gestures**: Mobile-friendly controls and navigation

### ⚙️ Advanced Features

- **WCA Inspection Timer**: 15-second compliant inspection with audio cues
- **Penalty System**: +2 and DNF penalty management
- **Solve History**: Complete solve log with filtering and search
- **Export/Import**: Backup and restore your data in JSON format
- **Customizable Settings**: Decimal precision, themes, audio preferences
- **FMC Mode**: Interactive solution input with move validation
- **Mobile Gestures**: Swipe controls for mobile devices

## 🚀 Quick Start

### Option 1: Direct Use

1. Visit the live demo: [scTimer Live](https://your-domain.com)
2. Start timing immediately - no installation required!

### Option 2: Local Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/scTimer.git
cd scTimer

# Open in browser
# Simply open index.html in your web browser
# Or serve with a local server for full PWA features
```

### Option 3: PWA Installation

1. Visit the web app in Chrome/Safari
2. Click "Install" when prompted
3. Use as a native app on your device

## 🎮 Usage Guide

### Basic Timer Operation

```
🔹 Hold SPACEBAR → Timer turns green → Release to start
🔹 Press SPACEBAR again → Stop timer
🔹 ESC → Cancel current solve
🔹 CTRL+Z → Delete last solve
🔹 Click Next (🔄) → Generate new scramble
```

### Stackmat Timer Setup

1. **Enable Stackmat Mode** in Settings
2. **Connect Y-splitter cable** to your computer's audio input
3. **Allow microphone access** when prompted
4. **Turn on Stackmat timer** - automatic detection and sync

> **Note**: Stackmat functionality requires desktop browsers with Web Audio API support. Mobile devices have limited compatibility due to hardware constraints.

### WCA Inspection Mode

1. **Enable "Use WCA Inspection (15s)"** in settings
2. **Press SPACEBAR once** → Start 15-second inspection
3. **Prepare your puzzle** during inspection
4. **Hold SPACEBAR** when ready → Timer turns green
5. **Release to start** → Begin solving

**WCA Inspection Rules:**

- ✅ **0-15 seconds**: Normal solve
- ⚠️ **15-17 seconds**: +2 penalty applied
- ❌ **17+ seconds**: DNF (Did Not Finish)

### FMC Mode

1. **Select FMC** from event dropdown
2. **Use virtual keyboard** to input solution moves
3. **Auto-validation** ensures legal move sequences
4. **Move counter** updates in real-time
5. **Submit solution** when complete

### Multi-Blind Solving

1. **Select "3x3x3 Multi-Blind"** from puzzle dropdown
2. **Enter number of cubes** to attempt (minimum 2)
3. **Generate scrambles** for all cubes
4. **View all scrambles** by clicking scramble area
5. **Start timer** when ready (inspection disabled for MBLD)
6. **Enter solved cubes** after stopping timer
7. **Result calculated** per WCA rules: (solved - (total - solved))

### Mobile Gestures

- **Swipe Down**: Delete recent solve
- **Swipe Up**: Add +2 penalty
- **Swipe Left/Right**: Generate new scramble
- **Long Press**: Access solve options

### Typing Mode

1. **Enable "Use Manual Input Timer"** in settings
2. **Enter times** in formats: `12.345`, `1:23.45`, `1:23:45.67`
3. **Press Enter** to submit time
4. **Inspection available** - press Space to start

### Keyboard Shortcuts

- **SPACEBAR**: Start/stop timer
- **ESC**: Cancel inspection or current solve
- **CTRL+Z**: Delete most recent solve
- **ENTER**: Submit time in typing mode

## 🛠️ Technical Details

### Browser Compatibility

| Feature      | Desktop                    | Mobile                        | Notes                  |
| ------------ | -------------------------- | ----------------------------- | ---------------------- |
| Basic Timer  | ✅ All browsers            | ✅ All browsers               | Full functionality     |
| Stackmat     | ✅ Chrome, Firefox, Safari | ❌ Limited                    | Web Audio API required |
| PWA Features | ✅ Modern browsers         | ✅ iOS Safari, Android Chrome | Offline support        |
| Audio Cues   | ✅ All browsers            | ✅ All browsers               | Inspection beeps       |

### Performance

- **Lightweight**: < 2MB total size
- **Fast Loading**: Critical resources prioritized
- **Efficient**: Minimal battery usage on mobile
- **Responsive**: 60fps animations and transitions

### Privacy & Data

- **Local Storage**: All data stored on your device
- **No Tracking**: No analytics or user tracking
- **Offline First**: Works completely offline
- **Export Control**: Full data ownership and portability

### Technologies Used

- **HTML5, CSS3, JavaScript (ES6+)**: Modern web standards
- **[cubing.js](https://js.cubing.net/cubing/)**: WCA-compliant scrambles and 2D visualization
- **[cubing-icons](https://icons.cubing.net/)**: Official WCA puzzle icons
- **[Stackmat Library](https://github.com/stilesdev/stackmat)**: Professional timer integration
- **Service Workers**: PWA offline functionality
- **Web Audio API**: Stackmat timer integration
- **LocalStorage**: Persistent data storage
- **Custom i18n**: Multi-language support with RTL layout

## 🔧 Development

### Project Structure

```
scTimer/
├── index.html              # Main application entry
├── manifest.json           # PWA configuration
├── service-worker.js       # Offline functionality
├── css/
│   ├── styles.css          # Main styles
│   ├── fonts.css           # Font definitions
│   └── fmc-manager.css     # FMC-specific styles
├── js/
│   ├── cubing-timer.js     # Core timer logic
│   ├── stackmat-manager.js # Stackmat integration
│   ├── fmc-manager.js      # FMC functionality
│   ├── language-manager.js # Internationalization
│   ├── stackmat.min.js     # Stackmat library
│   └── lang/               # Language files
├── assets/
│   ├── icons/              # PWA icons (72x72 to 512x512)
│   ├── sounds/             # Audio files (inspection beeps)
│   └── fonts/              # Custom fonts for RTL languages
└── stackmat-main/          # Stackmat library source
```

### Key Technologies

- **Vanilla JavaScript**: No framework dependencies
- **Web Audio API**: Stackmat timer integration
- **Service Workers**: PWA offline functionality
- **CSS Grid/Flexbox**: Responsive layouts
- **cubing.js**: Official WCA scramble generation

## 🤝 Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and test thoroughly
4. **Commit with clear messages**: `git commit -m 'Add amazing feature'`
5. **Push to your branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Development Guidelines

- Follow existing code style and patterns
- Test on multiple browsers and devices
- Update documentation for new features
- Ensure mobile compatibility
- Maintain performance standards

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[cubing.js](https://github.com/cubing/cubing.js)** - Official WCA scramble generation
- **[Stackmat Library](https://github.com/stilesdev/stackmat)** - Professional timer integration
- **[World Cube Association](https://www.worldcubeassociation.org/)** - Official regulations and scramble algorithms
- **Speedcubing Community** - Feedback, testing, and feature suggestions

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/yourusername/scTimer/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/scTimer/discussions)
- **Email**: <EMAIL>

---

**Happy Cubing! 🧩✨**
