name: Build

on:
  push:
    branches:
      - "*"
    paths-ignore:
      - "demo/**"
      - "example/**"

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: ["14", "16"]

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
