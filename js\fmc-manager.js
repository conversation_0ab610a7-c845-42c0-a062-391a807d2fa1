/**
 * Fewest Moves Challenge (FMC) Manager for scTimer
 * Handles FMC-specific functionality according to WCA regulations
 * Integrated directly into the main timer interface
 */

// We'll use dynamic imports to avoid 403 errors
let randomScrambleForEvent;
let Alg;
let TwistyPlayer;
let KPuzzle;
let puzzlesModule;

// Load the cubing.js libraries dynamically
async function loadCubingLibraries() {
  try {
    // Use the correct cubing.js CDN with proper paths
    const scrambleModule = await import(
      "https://cdn.cubing.net/js/cubing/scramble"
    );
    const algModule = await import("https://cdn.cubing.net/js/cubing/alg");
    const twistyModule = await import(
      "https://cdn.cubing.net/js/cubing/twisty"
    );
    const kpuzzleModule = await import(
      "https://cdn.cubing.net/js/cubing/kpuzzle"
    );
    puzzlesModule = await import("https://cdn.cubing.net/js/cubing/puzzles");

    randomScrambleForEvent = scrambleModule.randomScrambleForEvent;
    Alg = algModule.Alg;
    TwistyPlayer = twistyModule.TwistyPlayer;
    KPuzzle = kpuzzleModule.KPuzzle;

    // Cubing libraries loaded successfully
    return true;
  } catch (error) {
    // Failed to load cubing libraries - CDN may be unavailable

    // No fallback functions - only WCA official scrambles

    return false;
  }
}

// Note: Only WCA official scrambles are used - no fallback scrambles

// FMC state
let fmcScramble = "";
let fmcAttemptInProgress = false;
let fmcStartTime = 0;
let fmcTimerInterval = null;
let fmcElapsedTime = 0;
let fmcTimeLimit = 60 * 60 * 1000; // 60 minutes in milliseconds
let fmcSolution = "";
let fmcMoveCount = 0;
let fmcIsValid = false;
let fmcIsDNF = false;
let fmcDNFReason = "";

let fmcValidationUrl = ""; // URL for solution validation
let fmcIsReady = false; // Track if FMC is ready to start
let fmcIsLoading = false; // Track if FMC is currently loading

// DOM Elements
let fmcSolutionInput;
let fmcMoveCountDisplay;
let fmcTimerDisplay;
let fmcSubmitButton;
let fmcValidationMessage;
let fmcKeyboard;
let fmcVisualizationModal;
let fmcVisualizationContainer;
let mainTimerElement;
let mainScrambleElement;
let mainVisualizationContainer;
let mainStatsContainer;
let mainVisualizationBox;
let originalStatsContent;
let originalVisualizationContent;
let bottomContainer;

// Initialize FMC functionality
export async function initFMC() {
  // Load cubing libraries first
  const librariesLoaded = await loadCubingLibraries();
  if (!librariesLoaded) {
    // Failed to initialize FMC: Could not load required libraries
    return;
  }

  // Get main UI elements
  mainTimerElement = document.getElementById("timer");
  mainScrambleElement = document.getElementById("scramble");
  mainVisualizationContainer = document.getElementById(
    "visualization-container"
  );
  mainStatsContainer = document.querySelector(".stats-container");
  mainVisualizationBox = document.querySelector(".visualization");

  // FMC init - visualization box

  // Save original content to restore later
  if (mainStatsContainer) {
    originalStatsContent = mainStatsContainer.innerHTML;
  }

  if (mainVisualizationBox) {
    originalVisualizationContent = mainVisualizationBox.innerHTML;
  }

  // Create visualization modal if it doesn't exist
  createVisualizationModal();
}

// Create visualization modal
function createVisualizationModal() {
  // Check if modal already exists
  if (document.getElementById("fmc-visualization-modal")) {
    return;
  }

  // Create modal
  fmcVisualizationModal = document.createElement("div");
  fmcVisualizationModal.id = "fmc-visualization-modal";
  fmcVisualizationModal.className = "fmc-visualization-modal";

  // Create modal content
  const modalContent = document.createElement("div");
  modalContent.className = "fmc-visualization-modal-content";

  // Create header
  const modalHeader = document.createElement("div");
  modalHeader.className = "fmc-visualization-modal-header";

  // Create title
  const modalTitle = document.createElement("div");
  modalTitle.className = "fmc-visualization-modal-title";
  modalTitle.textContent = "Cube Visualization";

  // Create close button
  const closeButton = document.createElement("button");
  closeButton.className = "fmc-visualization-modal-close";
  closeButton.innerHTML = '<i class="fas fa-times"></i>';
  closeButton.addEventListener("click", () => {
    fmcVisualizationModal.classList.remove("show");
  });

  // Add title and close button to header
  modalHeader.appendChild(modalTitle);
  modalHeader.appendChild(closeButton);

  // Create visualization container
  fmcVisualizationContainer = document.createElement("div");
  fmcVisualizationContainer.className = "fmc-visualization-container";

  // Add header and visualization container to modal content
  modalContent.appendChild(modalHeader);
  modalContent.appendChild(fmcVisualizationContainer);

  // Add modal content to modal
  fmcVisualizationModal.appendChild(modalContent);

  // Add modal to body
  document.body.appendChild(fmcVisualizationModal);
}

// Set up the FMC interface
export function setupFMCInterface() {
  // Make sure the timer is visible immediately using the global function if available
  if (typeof window.ensureTimerVisible === "function") {
    window.ensureTimerVisible();
  } else {
    // Fallback if global function is not available
    const timerElement = document.getElementById("timer");
    if (timerElement) {
      timerElement.style.display = "block";
      timerElement.style.visibility = "visible";
      timerElement.style.opacity = "1";
    }
  }

  // Keep scramble hidden until attempt starts
  if (mainScrambleElement) {
    mainScrambleElement.style.visibility = "hidden";

    // Keep scramble text hidden
    const scrambleText = document.getElementById("scramble-text");
    if (scrambleText && scrambleText.textContent) {
      scrambleText.classList.add("hidden");
    }
  }

  // Hide visualization container but don't remove it completely
  if (mainVisualizationContainer) {
    // Save the original display style if not already saved
    if (!mainVisualizationContainer.dataset.originalDisplay) {
      mainVisualizationContainer.dataset.originalDisplay =
        getComputedStyle(mainVisualizationContainer).display || "flex";
    }

    // Hide the container
    mainVisualizationContainer.style.display = "none";

    // Don't remove the twisty player, just hide it
    const existingTwistyPlayer =
      mainVisualizationContainer.querySelector("twisty-player");
    if (existingTwistyPlayer) {
      // Save the original display style if not already saved
      if (!existingTwistyPlayer.dataset.originalDisplay) {
        existingTwistyPlayer.dataset.originalDisplay =
          getComputedStyle(existingTwistyPlayer).display || "block";
      }

      // Hide the twisty player
      existingTwistyPlayer.style.display = "none";
    }
  }

  // Set up FMC-specific stats display
  setupFMCStatsDisplay();

  // Create FMC keyboard in the bottom area
  createFMCKeyboard();

  // Set up the timer to show loading state initially
  if (mainTimerElement) {
    // Save original timer text if needed
    if (!mainTimerElement.dataset.originalText) {
      mainTimerElement.dataset.originalText = mainTimerElement.textContent;
    }

    // Make sure the timer is visible
    mainTimerElement.style.display = "block";
    mainTimerElement.style.visibility = "visible";
    mainTimerElement.style.opacity = "1";

    // Show loading state initially
    showFMCLoadingState();
  }

  // Start the loading process
  initializeFMCAttempt();
}

// Show FMC loading state
function showFMCLoadingState() {
  if (mainTimerElement) {
    fmcIsLoading = true;
    fmcIsReady = false;

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const fmcTranslations = translations.fmc || {};

    // Create loading content with spinner
    const loadingContent = `
      <div class="fmc-loading-container">
        <div class="fmc-loading-spinner"></div>
        <div class="fmc-loading-text">${
          fmcTranslations.loadingFMC || "Loading FMC..."
        }</div>
        <div class="fmc-loading-subtext">${
          fmcTranslations.generatingScramble ||
          "Generating scramble and preparing interface"
        }</div>
      </div>
    `;

    mainTimerElement.innerHTML = loadingContent;
    mainTimerElement.classList.remove("fmc-start-prompt");
    mainTimerElement.classList.add("fmc-loading");

    // Disable space key interaction
    document.removeEventListener("keydown", handleFMCSpaceKey);
  }
}

// Show FMC ready state
function showFMCReadyState() {
  if (mainTimerElement) {
    fmcIsLoading = false;
    fmcIsReady = true;

    // Get translations
    const translations = window.i18nModule?.translations || {};
    const fmcTranslations = translations.fmc || {};

    // Restore text content (remove HTML from loading state)
    mainTimerElement.innerHTML = "";
    mainTimerElement.textContent =
      fmcTranslations.pressToStart || "Press space to start FMC attempt";
    mainTimerElement.classList.remove("fmc-loading");
    mainTimerElement.classList.add("fmc-start-prompt");

    // Enable space key interaction
    document.addEventListener("keydown", handleFMCSpaceKey);
  }
}

// Initialize FMC attempt (load scramble and prepare everything)
async function initializeFMCAttempt() {
  try {
    // Make sure cubing libraries are loaded
    if (!randomScrambleForEvent) {
      const librariesLoaded = await loadCubingLibraries();
      if (!librariesLoaded) {
        throw new Error("Failed to load cubing libraries");
      }
    }

    // Generate scramble
    const scramble = await generateFMCScramble();

    if (!scramble) {
      throw new Error("Failed to generate scramble");
    }

    // Show ready state
    showFMCReadyState();
  } catch (error) {
    // Error initializing FMC

    // Show error state
    if (mainTimerElement) {
      // Restore text content (remove HTML from loading state)
      mainTimerElement.innerHTML = "";
      mainTimerElement.textContent =
        "Error loading FMC. Please refresh the page.";
      mainTimerElement.classList.remove("fmc-loading", "fmc-start-prompt");
      mainTimerElement.classList.add("fmc-error");
      mainTimerElement.style.color = "#e74c3c";
    }

    fmcIsLoading = false;
    fmcIsReady = false;
  }
}

// Handle space key for FMC
function handleFMCSpaceKey(event) {
  if (
    event.code === "Space" &&
    fmcIsReady &&
    !fmcIsLoading &&
    !fmcAttemptInProgress
  ) {
    event.preventDefault();
    startFMCAttempt();
  }
}

// Set up FMC-specific stats display
export function setupFMCStatsDisplay() {
  // Make sure stats container is visible before FMC attempt
  const statsContainer = document.querySelector(".stats-container");
  if (!statsContainer) {
    // Stats container not found
    return;
  }

  // Save original stats content if not already saved
  if (!originalStatsContent) {
    originalStatsContent = statsContainer.innerHTML;
  }

  // Show stats container
  statsContainer.classList.remove("hidden");

  // Enable stats checkbox
  const showStatsCheckbox = document.getElementById("show-stats");
  if (showStatsCheckbox) {
    showStatsCheckbox.disabled = false;
    showStatsCheckbox.checked = true;
    if (showStatsCheckbox.parentElement) {
      showStatsCheckbox.parentElement.classList.remove("disabled");
    }
  }

  // Get FMC times from the global timesMap
  let times = [];

  // First try to get times from the global timesMap
  if (window.timesMap && window.timesMap["333fm"]) {
    times = window.timesMap["333fm"];
  } else {
    // Fallback to localStorage if timesMap is not available
    const currentEvent = "333fm";
    const timesKey = `scTimer-times`;
    const timesJson = localStorage.getItem(timesKey) || "{}";

    try {
      const allTimes = JSON.parse(timesJson);
      times = allTimes[currentEvent] || [];
    } catch (error) {
      // Error parsing FMC times
      times = [];
    }
  }

  // Loaded FMC times

  // Extract move counts from times
  const moveCounts = times
    .map((time) => {
      // For FMC, check if it's a DNF
      if (time.isDNF || time.time === "DNF" || time.time === Infinity) {
        return null;
      }

      // Get move count from the moveCount property or time property
      if (time.isFMC && typeof time.moveCount === "number") {
        return time.moveCount;
      } else if (typeof time.time === "number") {
        return time.time;
      }

      return null;
    })
    .filter((count) => count !== null && !isNaN(count));

  // Extracted move counts

  // Calculate statistics
  const totalAttempts = times.length;
  const bestMoveCount = moveCounts.length > 0 ? Math.min(...moveCounts) : "-";
  const worstMoveCount = moveCounts.length > 0 ? Math.max(...moveCounts) : "-";

  // Calculate averages
  const ao5 = calculateAverage(moveCounts, 5);
  const ao12 = calculateAverage(moveCounts, 12);
  const mean = calculateMean(moveCounts);

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const fmcTranslations = translations.fmc || {};
  const statsTranslations = translations.stats || {};

  // Create FMC-specific stats HTML with two-column layout
  const statsHTML = `
    <div class="stats-content fmc-stats-content">
      <div class="stats-row">
        <div class="stats-label">${
          statsTranslations.attempts || "Attempts"
        }:</div>
        <div class="stats-value">${totalAttempts}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.best || "Best"}:</div>
        <div class="stats-value">${bestMoveCount} ${
    fmcTranslations.moves || "mvs"
  }</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.worst || "Worst"}:</div>
        <div class="stats-value">${worstMoveCount} ${
    fmcTranslations.moves || "mvs"
  }</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.mean || "Mean"}:</div>
        <div class="stats-value">${mean} ${fmcTranslations.moves || "mvs"}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.ao5 || "ao5"}:</div>
        <div class="stats-value">${ao5} ${fmcTranslations.moves || "mvs"}</div>
      </div>
      <div class="stats-row">
        <div class="stats-label">${statsTranslations.ao12 || "ao12"}:</div>
        <div class="stats-value">${ao12} ${fmcTranslations.moves || "mvs"}</div>
      </div>
    </div>
  `;

  // Update stats container
  statsContainer.innerHTML = statsHTML;
}

// Helper function to calculate average of N
function calculateAverage(values, n) {
  if (values.length < n) return "-";

  // Get the most recent n values
  const recentValues = values.slice(-n);

  // For FMC, we don't remove best and worst
  const sum = recentValues.reduce((acc, val) => acc + val, 0);
  const avg = sum / recentValues.length;

  return avg.toFixed(2);
}

// Helper function to calculate mean
function calculateMean(values) {
  if (values.length === 0) return "-";

  const sum = values.reduce((acc, val) => acc + val, 0);
  const mean = sum / values.length;

  return mean.toFixed(2);
}

// Function to update FMC keyboard visibility
export function updateFMCKeyboardVisibility(show) {
  // Make this function available globally
  window.updateFMCKeyboardVisibility = updateFMCKeyboardVisibility;

  // Find the keyboard container if it's not already set
  if (!bottomContainer) {
    bottomContainer = document.getElementById("fmc-keyboard-container");
    if (!bottomContainer) return;
  }

  // Updating FMC keyboard visibility

  if (show) {
    // Show the full keyboard
    bottomContainer.style.display = "block";
    bottomContainer.style.visibility = "visible";
    bottomContainer.style.opacity = "1";

    // Make sure the timer is in the keyboard
    const timerCell = document.getElementById("fmc-keyboard-timer-cell");
    if (timerCell && fmcTimerDisplay) {
      // Update the timer in the keyboard
      const keyboardTimer = document.getElementById("fmc-keyboard-timer");
      if (keyboardTimer) {
        keyboardTimer.textContent = fmcTimerDisplay.textContent;
      }
    }

    // Hide the standalone timer if it's in the solution container
    if (
      fmcTimerDisplay &&
      fmcTimerDisplay.parentNode ===
        document.getElementById("fmc-solution-container")
    ) {
      fmcTimerDisplay.style.display = "none";
    }
  } else {
    // Only hide the top 3 rows, keep the bottom row visible
    if (bottomContainer) {
      bottomContainer.style.display = "block";

      // Get all keyboard buttons
      const keyboard = document.querySelector(".fmc-keyboard");
      if (keyboard) {
        // Get all buttons that are not in the last row (first 18 buttons)
        // and are not special buttons
        const regularButtons = Array.from(keyboard.children).slice(0, 18);

        // Hide regular buttons (top 3 rows)
        regularButtons.forEach((button) => {
          button.style.display = "none";
        });

        // Make sure special buttons in the last row are visible
        const specialButtons = Array.from(keyboard.children).slice(18);

        specialButtons.forEach((button) => {
          button.style.display =
            button.classList.contains("fmc-keyboard-timer-cell") ||
            button.classList.contains("fmc-keyboard-move-count-cell")
              ? "flex"
              : "block";
        });
      }
    }
  }
}

// Start FMC attempt
export function startFMCAttempt() {
  // Check if FMC is ready
  if (!fmcIsReady || fmcIsLoading) {
    return;
  }

  // Check if scramble is available
  if (!fmcScramble) {
    return;
  }

  // Reset state
  fmcAttemptInProgress = true;
  fmcIsReady = false; // Prevent multiple starts
  fmcStartTime = Date.now();
  fmcElapsedTime = 0;
  fmcSolution = "";
  fmcMoveCount = 0;
  fmcIsValid = false;
  fmcIsDNF = false;
  fmcDNFReason = "";

  // Make sure the timer is visible using the global function if available
  if (typeof window.ensureTimerVisible === "function") {
    window.ensureTimerVisible();
  } else {
    // Fallback if global function is not available
    if (mainTimerElement) {
      mainTimerElement.style.display = "block";
      mainTimerElement.style.visibility = "visible";
      mainTimerElement.style.opacity = "1";
    }
  }

  // Generate FMC scramble if not already generated
  if (!fmcScramble) {
    generateFMCScramble().then((scramble) => {
      // Update the global currentScramble
      if (typeof window.currentScramble !== "undefined") {
        window.currentScramble = scramble;
      }

      // Show scramble after it's generated
      if (mainScrambleElement) {
        mainScrambleElement.style.visibility = "visible";

        // Show scramble text
        const scrambleText = document.getElementById("scramble-text");
        if (scrambleText) {
          scrambleText.textContent = scramble;
          scrambleText.classList.remove("hidden");
        }
      }
    });
  } else {
    // Update the global currentScramble
    if (typeof window.currentScramble !== "undefined") {
      window.currentScramble = fmcScramble;
    }

    // Show scramble
    if (mainScrambleElement) {
      mainScrambleElement.style.visibility = "visible";

      // Show scramble text
      const scrambleText = document.getElementById("scramble-text");
      if (scrambleText) {
        scrambleText.textContent = fmcScramble;
        scrambleText.classList.remove("hidden");
      }
    }
  }

  // Hide stats container during FMC attempt
  const statsContainer = document.querySelector(".stats-container");
  if (statsContainer) {
    statsContainer.classList.add("hidden");
  }

  // Disable stats checkbox during FMC attempt
  const showStatsCheckbox = document.getElementById("show-stats");
  if (showStatsCheckbox) {
    showStatsCheckbox.disabled = true;
    showStatsCheckbox.checked = false;
    if (showStatsCheckbox.parentElement) {
      showStatsCheckbox.parentElement.classList.add("disabled");
    }
  }

  // Create solution interface
  createFMCSolutionInterface();

  // Check if keyboard should be shown based on settings
  const showFMCKeyboard = localStorage.getItem("scTimer-showFMCKeyboard");
  const shouldShowKeyboard = showFMCKeyboard !== "false"; // Default to true

  // Update keyboard visibility based on settings
  updateFMCKeyboardVisibility(shouldShowKeyboard);

  // Start timer
  startFMCTimer();

  // Force an immediate timer display update to ensure it's visible
  setTimeout(() => {
    updateFMCTimerDisplay();
  }, 100);
}

// Create the FMC solution input interface
function createFMCSolutionInterface() {
  // Create container for the solution input
  const solutionContainer = document.createElement("div");
  solutionContainer.className = "fmc-solution-container";
  solutionContainer.id = "fmc-solution-container";

  // Create solution input
  fmcSolutionInput = document.createElement("textarea");
  fmcSolutionInput.className = "fmc-solution-input";
  fmcSolutionInput.id = "fmc-solution-input";
  fmcSolutionInput.placeholder =
    "Enter your solution here using standard WCA notation...";

  // Create move count display (inside input area, bottom right)
  fmcMoveCountDisplay = document.createElement("div");
  fmcMoveCountDisplay.className = "fmc-move-count-display";
  fmcMoveCountDisplay.id = "fmc-move-count-display";
  fmcMoveCountDisplay.textContent = "Moves: 0";

  // Create timer display (will be moved to keyboard grid if keyboard is visible)
  fmcTimerDisplay = document.createElement("div");
  fmcTimerDisplay.className = "fmc-timer-display";
  fmcTimerDisplay.id = "fmc-timer-display";
  fmcTimerDisplay.textContent = "00:00";

  // Create validation message (hidden until submission)
  fmcValidationMessage = document.createElement("div");
  fmcValidationMessage.className = "fmc-validation-message";
  fmcValidationMessage.id = "fmc-validation-message";
  fmcValidationMessage.style.display = "none";

  // Add elements to container
  solutionContainer.appendChild(fmcSolutionInput);
  solutionContainer.appendChild(fmcValidationMessage);

  // Create keyboard if it doesn't exist
  if (!bottomContainer) {
    createFMCKeyboard();
  }

  // Add keyboard to solution container
  if (bottomContainer) {
    solutionContainer.appendChild(bottomContainer);
  }

  // Add solution interface to timer's parent and hide main timer
  if (mainTimerElement && mainTimerElement.parentNode) {
    mainTimerElement.style.display = "none";
    mainTimerElement.parentNode.appendChild(solutionContainer);
  }

  // Set up event listeners
  fmcSolutionInput.addEventListener("input", updateMoveCount);

  // Add event listener for keydown to handle space and enter keys
  fmcSolutionInput.addEventListener("keydown", (event) => {
    // Allow space key to work normally in the input
    if (event.key === " " || event.code === "Space") {
      // Stop propagation to prevent main timer from handling this space key
      event.stopPropagation();

      // Don't prevent default - let the space be added to the input

      // Update move count after a short delay to allow the space to be added
      setTimeout(() => {
        updateMoveCount();
      }, 10);
    }

    // Handle Enter key to create line breaks instead of submitting
    if (event.key === "Enter" || event.code === "Enter") {
      // Stop propagation and prevent default form submission
      event.stopPropagation();
      event.preventDefault();

      // Insert line break at cursor position
      const start = fmcSolutionInput.selectionStart;
      const end = fmcSolutionInput.selectionEnd;
      const text = fmcSolutionInput.value;

      fmcSolutionInput.value = text.slice(0, start) + "\n" + text.slice(end);

      // Update cursor position
      const newPosition = start + 1;
      fmcSolutionInput.selectionStart = newPosition;
      fmcSolutionInput.selectionEnd = newPosition;

      // Update move count after line break
      setTimeout(() => {
        updateMoveCount();
      }, 10);
    }
  });

  // Add event listener for keyup to handle space key
  fmcSolutionInput.addEventListener("keyup", (event) => {
    // Allow space key to work normally in the input
    if (event.key === " " || event.code === "Space") {
      // Stop propagation to prevent main timer from handling this space key
      event.stopPropagation();
    }
  });

  // Focus on the solution input
  setTimeout(() => {
    fmcSolutionInput.focus();
  }, 100);
}

// Create FMC keyboard integrated with the solution input
function createFMCKeyboard() {
  // Save original content if containers exist
  if (mainStatsContainer) {
    originalStatsContent = mainStatsContainer.innerHTML;
  }

  if (mainVisualizationBox) {
    originalVisualizationContent = mainVisualizationBox.innerHTML;
  }

  // Create keyboard container that will be placed below the solution input
  bottomContainer = document.createElement("div");
  bottomContainer.className = "fmc-keyboard-container";
  bottomContainer.id = "fmc-keyboard-container";

  // Style the container - remove background color to let CSS handle it
  bottomContainer.style.width = "100%";
  bottomContainer.style.marginTop = "10px";
  bottomContainer.style.borderRadius = "8px";
  bottomContainer.style.padding = "10px";
  bottomContainer.style.boxSizing = "border-box";
  bottomContainer.style.display = "none"; // Initially hidden

  // Create keyboard
  const keyboard = document.createElement("div");
  keyboard.className = "fmc-keyboard";
  keyboard.id = "fmc-keyboard";
  keyboard.style.display = "grid";
  keyboard.style.gridTemplateColumns = "repeat(6, 1fr)";
  keyboard.style.gap = "5px";
  keyboard.style.width = "100%";

  // Check if keyboard should be hidden based on saved preference
  const showFMCKeyboard = localStorage.getItem("scTimer-showFMCKeyboard");
  if (showFMCKeyboard === "false") {
    keyboard.classList.add("rows-hidden");
  }

  // Define all keyboard keys in a 4x6 grid with special buttons at the bottom
  // Last row includes Hide/Show, Visualization, Timer+Moves, Submit
  const keys = [
    "R",
    "L",
    "U",
    "D",
    "F",
    "B",
    "M",
    "S",
    "E",
    "w",
    "2",
    "'",
    "Enter",
    "x",
    "y",
    "z",
    "Space",
    "⌫",
    "KEYBOARD",
    "👁️",
    "TIMER_MOVES", // Combined cell for Timer and Moves (spans 2 columns)
    "Submit", // Submit button (spans 2 columns)
  ];

  // Create all keyboard buttons
  keys.forEach((key, index) => {
    // Handle combined TIMER_MOVES cell
    if (key === "TIMER_MOVES") {
      // Skip if we already created this cell
      if (keyboard.querySelector(".fmc-keyboard-timer-moves-cell")) {
        return;
      }

      // Create a combined cell for timer and move count
      const combinedCell = document.createElement("div");
      combinedCell.className = "fmc-keyboard-timer-moves-cell";
      combinedCell.id = "fmc-keyboard-timer-moves-cell";
      combinedCell.style.display = "flex";
      combinedCell.style.flexDirection = "column";
      combinedCell.style.alignItems = "center";
      combinedCell.style.justifyContent = "center";
      combinedCell.style.border = "none";
      combinedCell.style.borderRadius = "4px";
      combinedCell.style.padding = "8px";
      combinedCell.style.gridColumn = "span 2"; // Make it span 2 columns
      combinedCell.dataset.special = "true"; // Mark as special to keep visible

      // Create timer container
      const timerContainer = document.createElement("div");
      timerContainer.className = "fmc-keyboard-timer-container";
      timerContainer.style.marginBottom = "5px";
      timerContainer.style.width = "100%";
      timerContainer.style.textAlign = "center";

      // Create the timer display for the keyboard
      const keyboardTimer = document.createElement("div");
      keyboardTimer.className = "fmc-keyboard-timer";
      keyboardTimer.id = "fmc-keyboard-timer";
      keyboardTimer.textContent = "00:00"; // Initial display
      keyboardTimer.style.position = "static";
      keyboardTimer.style.padding = "0";
      keyboardTimer.style.opacity = "1";
      keyboardTimer.style.fontWeight = "bold";
      keyboardTimer.style.fontSize = "1.2rem";
      keyboardTimer.style.color = "var(--timer-color, #2196f3)";

      timerContainer.appendChild(keyboardTimer);

      // Create move count container
      const moveCountContainer = document.createElement("div");
      moveCountContainer.className = "fmc-keyboard-move-count-container";
      moveCountContainer.style.width = "100%";
      moveCountContainer.style.textAlign = "center";

      // Create move count display
      const keyboardMoveCount = document.createElement("div");
      keyboardMoveCount.className = "fmc-keyboard-move-count";
      keyboardMoveCount.id = "fmc-keyboard-move-count";
      keyboardMoveCount.textContent = "Moves: 0";
      keyboardMoveCount.style.position = "static";
      keyboardMoveCount.style.padding = "0";
      keyboardMoveCount.style.fontWeight = "bold";
      keyboardMoveCount.style.fontSize = "0.9rem";

      moveCountContainer.appendChild(keyboardMoveCount);

      // Add both containers to the combined cell
      combinedCell.appendChild(timerContainer);
      combinedCell.appendChild(moveCountContainer);

      keyboard.appendChild(combinedCell);
      return;
    }

    // Handle KEYBOARD cell (Hide/Show button)
    if (key === "KEYBOARD") {
      // Create a button for toggling keyboard visibility
      const keyboardButton = document.createElement("button");
      keyboardButton.className = "fmc-keyboard-toggle";
      keyboardButton.id = "fmc-keyboard-toggle";

      // Check current visibility state
      const showFMCKeyboard = localStorage.getItem("scTimer-showFMCKeyboard");
      const isVisible = showFMCKeyboard !== "false"; // Default to true

      // Set icon only
      keyboardButton.innerHTML = '<i class="fas fa-keyboard"></i>';
      keyboardButton.title = isVisible ? "Hide Keyboard" : "Show Keyboard";

      // Style the button - remove inline colors to let CSS handle them
      keyboardButton.style.display = "flex";
      keyboardButton.style.alignItems = "center";
      keyboardButton.style.justifyContent = "center";
      keyboardButton.style.border = "none";
      keyboardButton.style.borderRadius = "4px";
      keyboardButton.style.padding = "8px";
      keyboardButton.style.cursor = "pointer";
      keyboardButton.dataset.special = "true"; // Mark as special button to keep visible

      // Add event listener
      keyboardButton.addEventListener("click", () => {
        // Get all keyboard buttons except those in the last row
        const keyboard = document.querySelector(".fmc-keyboard");
        if (!keyboard) return;

        // Check if keyboard rows are currently visible
        const isVisible = !keyboard.classList.contains("rows-hidden");

        // Toggle visibility
        localStorage.setItem(
          "scTimer-showFMCKeyboard",
          isVisible ? "false" : "true"
        );

        // Update keyboard visibility using CSS class
        if (isVisible) {
          keyboard.classList.add("rows-hidden");
        } else {
          keyboard.classList.remove("rows-hidden");
        }

        // Update button title to reflect current state
        keyboardButton.title = isVisible ? "Show Keyboard" : "Hide Keyboard";
      });

      keyboard.appendChild(keyboardButton);
      return;
    }

    // Create button
    const button = document.createElement("button");
    button.className = "fmc-key";
    button.textContent = key;

    // Basic button styling - remove background and color to let CSS handle it
    button.style.padding = "0";
    button.style.border = "none";
    button.style.borderRadius = "4px";
    button.style.fontSize = "1rem";
    button.style.cursor = "pointer";
    button.style.display = "flex";
    button.style.alignItems = "center";
    button.style.justifyContent = "center";

    // Style special buttons - remove inline colors to let CSS handle them
    if (key === "Submit") {
      // Get translations for Submit button
      const translations = window.i18nModule?.translations || {};
      const fmcTranslations = translations.fmc || {};
      button.textContent = fmcTranslations.submit || "Submit";
      button.style.gridColumn = "span 2"; // Make Submit button span 2 columns
      button.dataset.special = "true"; // Mark as special button to keep visible
    } else if (key === "Enter") {
      // Enter button creates line breaks - use return/enter icon
      button.innerHTML = '<i class="fas fa-level-down-alt"></i>'; // Use enter/return icon
    } else if (key === "⌫") {
      // Backspace button - CSS will handle styling
    } else if (key === "Space") {
      button.textContent = "␣"; // Use space symbol
    } else if (key === "👁️") {
      button.innerHTML = '<i class="fas fa-cube"></i>'; // Use cube icon
      button.dataset.special = "true"; // Mark as special button to keep visible
    }

    // Add event listeners
    button.addEventListener("click", () => {
      handleFMCKeyPress(key);
    });

    // Add button to keyboard
    keyboard.appendChild(button);
  });

  // Add keyboard to container
  bottomContainer.appendChild(keyboard);
}

// Handle WCA official notation with auto-spacing and validation
function handleWCANotation(key, text, start, end) {
  // WCA official face moves
  const faceMoves = ["U", "D", "L", "R", "F", "B"];
  // WCA official slice moves
  const sliceMoves = ["M", "E", "S"];
  // WCA official rotations
  const rotations = ["x", "y", "z"];
  // Modifiers
  const modifiers = ["w", "'", "2"];

  // Get the character before cursor to understand context
  const charBefore = start > 0 ? text.charAt(start - 1) : "";
  const wordBefore = getWordBefore(text, start);

  // Handle modifiers (w, ', 2)
  if (modifiers.includes(key)) {
    return handleModifier(key, text, start, end, charBefore, wordBefore);
  }

  // Handle face moves, slice moves, and rotations
  if (
    faceMoves.includes(key) ||
    sliceMoves.includes(key) ||
    rotations.includes(key)
  ) {
    return handleMove(key, text, start, end, charBefore);
  }

  // Invalid key
  return { valid: false };
}

// Get the word before the cursor position
function getWordBefore(text, position) {
  let word = "";
  for (let i = position - 1; i >= 0; i--) {
    const char = text.charAt(i);
    if (char === " " || char === "\n") {
      break;
    }
    word = char + word;
  }
  return word;
}

// Handle modifier keys (w, ', 2)
function handleModifier(key, text, start, end, charBefore, wordBefore) {
  // Modifiers cannot be used alone
  if (!wordBefore || wordBefore.match(/^\s*$/)) {
    return { valid: false };
  }

  // Check what the modifier is being applied to
  const baseMove = wordBefore.replace(/[w'2]/g, ""); // Remove existing modifiers

  // w modifier rules
  if (key === "w") {
    // w can only be applied to face moves (U, D, L, R, F, B)
    const faceMoves = ["U", "D", "L", "R", "F", "B"];
    if (!faceMoves.includes(baseMove)) {
      return { valid: false }; // w cannot be applied to slice moves or rotations
    }

    // w cannot be applied if already has w
    if (wordBefore.includes("w")) {
      return { valid: false };
    }
  }

  // ' modifier rules
  if (key === "'") {
    // ' cannot be applied if already has '
    if (wordBefore.includes("'")) {
      return { valid: false };
    }

    // ' cannot be applied if already has 2
    if (wordBefore.includes("2")) {
      return { valid: false };
    }
  }

  // 2 modifier rules
  if (key === "2") {
    // 2 cannot be applied if already has 2
    if (wordBefore.includes("2")) {
      return { valid: false };
    }

    // 2 cannot be applied if already has '
    if (wordBefore.includes("'")) {
      return { valid: false };
    }
  }

  // Valid modifier - construct properly ordered notation
  let newWord = constructProperNotation(wordBefore, key);

  // Calculate the position where the word starts
  const wordStartPos = start - wordBefore.length;

  // Replace the entire word with the properly ordered notation
  const newText = text.slice(0, wordStartPos) + newWord + text.slice(end);
  const newPosition = wordStartPos + newWord.length;

  return {
    valid: true,
    newText: newText,
    newPosition: newPosition,
  };
}

// Construct properly ordered WCA notation
function constructProperNotation(currentWord, newModifier) {
  // Extract components from current word
  const baseMove = currentWord.replace(/[w'2]/g, "");
  const hasW = currentWord.includes("w");
  const hasPrime = currentWord.includes("'");
  const hasTwo = currentWord.includes("2");

  // Add the new modifier
  let finalHasW = hasW;
  let finalHasPrime = hasPrime;
  let finalHasTwo = hasTwo;

  if (newModifier === "w") finalHasW = true;
  if (newModifier === "'") finalHasPrime = true;
  if (newModifier === "2") finalHasTwo = true;

  // Construct in proper order: BaseMove + w + (2 or ')
  let result = baseMove;

  if (finalHasW) {
    result += "w";
  }

  if (finalHasTwo) {
    result += "2";
  } else if (finalHasPrime) {
    result += "'";
  }

  return result;
}

// Handle move keys (face moves, slice moves, rotations)
function handleMove(key, text, start, end, charBefore) {
  let insertText = key;

  // Determine if we need a space before the move
  const needsSpace = shouldAddSpace(charBefore);

  if (needsSpace) {
    insertText = " " + insertText;
  }

  // Insert the move
  const newText = text.slice(0, start) + insertText + text.slice(end);
  const newPosition = start + insertText.length;

  return {
    valid: true,
    newText: newText,
    newPosition: newPosition,
  };
}

// Determine if a space should be added before a move
function shouldAddSpace(charBefore) {
  // No space needed at the beginning
  if (!charBefore) {
    return false;
  }

  // No space needed after whitespace or newline
  if (charBefore === " " || charBefore === "\n") {
    return false;
  }

  // Space needed after any other character (end of previous move)
  return true;
}

// Handle FMC keyboard button press
function handleFMCKeyPress(key) {
  if (!fmcSolutionInput) return;

  // Get current cursor position and selection
  const start = fmcSolutionInput.selectionStart;
  const end = fmcSolutionInput.selectionEnd;
  const text = fmcSolutionInput.value;

  // Handle different keys
  switch (key) {
    case "Enter":
      // Insert a line break
      fmcSolutionInput.value = text.slice(0, start) + "\n" + text.slice(end);

      // Update cursor position
      const newLinePosition = start + 1;
      fmcSolutionInput.selectionStart = newLinePosition;
      fmcSolutionInput.selectionEnd = newLinePosition;
      break;

    case "Space":
      // Insert a space
      fmcSolutionInput.value = text.slice(0, start) + " " + text.slice(end);

      // Update cursor position
      const newSpacePosition = start + 1;
      fmcSolutionInput.selectionStart = newSpacePosition;
      fmcSolutionInput.selectionEnd = newSpacePosition;
      break;

    case "👁️":
      // Show visualization
      showVisualization();
      break;

    case "⌫":
      // Backspace - delete selected text or character before cursor
      if (start === end) {
        // No selection, delete character before cursor
        if (start > 0) {
          fmcSolutionInput.value = text.slice(0, start - 1) + text.slice(end);
          fmcSolutionInput.selectionStart = start - 1;
          fmcSolutionInput.selectionEnd = start - 1;
        }
      } else {
        // Delete selected text
        fmcSolutionInput.value = text.slice(0, start) + text.slice(end);
        fmcSolutionInput.selectionStart = start;
        fmcSolutionInput.selectionEnd = start;
      }
      break;

    case "Submit":
      // Submit solution
      submitFMCSolution();
      break;

    default:
      // Handle WCA official notation with auto-spacing and validation
      const result = handleWCANotation(key, text, start, end);

      if (result.valid) {
        // Insert the validated text
        fmcSolutionInput.value = result.newText;

        // Update cursor position
        fmcSolutionInput.selectionStart = result.newPosition;
        fmcSolutionInput.selectionEnd = result.newPosition;
      }
      // If not valid, ignore the key press (no action)
      break;
  }

  // Trigger input event to update move count
  const inputEvent = new Event("input", { bubbles: true });
  fmcSolutionInput.dispatchEvent(inputEvent);

  // Focus back on the input
  fmcSolutionInput.focus();
}

// Update move count based on solution input
function updateMoveCount() {
  if (!fmcSolutionInput) return;

  // Get solution text and clean up multiple spaces
  let solution = fmcSolutionInput.value;

  // Replace multiple consecutive spaces with single space
  const cleanedSolution = solution.replace(/[ ]+/g, " ");

  // Update the input if cleaning was needed
  if (cleanedSolution !== solution) {
    const cursorPosition = fmcSolutionInput.selectionStart;
    fmcSolutionInput.value = cleanedSolution;

    // Restore cursor position (adjust if text was shortened)
    const newPosition = Math.min(cursorPosition, cleanedSolution.length);
    fmcSolutionInput.selectionStart = newPosition;
    fmcSolutionInput.selectionEnd = newPosition;
  }

  // Use cleaned solution for processing
  fmcSolution = cleanedSolution.trim();

  // Count moves (using Half-Turn Metric)
  const moveCount = countMoves(fmcSolution);
  fmcMoveCount = moveCount;

  // Get translations
  const translations = window.i18nModule?.translations || {};
  const fmcTranslations = translations.fmc || {};

  // Update main move count display
  if (fmcMoveCountDisplay) {
    fmcMoveCountDisplay.textContent = `${
      fmcTranslations.moveCount || "Moves:"
    } ${moveCount}`;
  }

  // Update keyboard move count display if it exists
  const keyboardMoveCount = document.getElementById("fmc-keyboard-move-count");
  if (keyboardMoveCount) {
    keyboardMoveCount.textContent = `${
      fmcTranslations.moveCount || "Moves:"
    } ${moveCount}`;
  }
}

// Count moves in a solution using Half-Turn Metric
function countMoves(solution) {
  return countHTM(solution);
}

// Start FMC timer
function startFMCTimer() {
  // Reset timer
  fmcStartTime = Date.now();
  fmcElapsedTime = 0;

  // Ensure timer variables are properly initialized
  if (!fmcStartTime || isNaN(fmcStartTime)) {
    fmcStartTime = Date.now();
  }

  // Update timer display
  updateFMCTimerDisplay();

  // Start interval
  fmcTimerInterval = setInterval(() => {
    // Calculate elapsed time
    fmcElapsedTime = Date.now() - fmcStartTime;

    // Check if time limit reached
    if (fmcElapsedTime >= fmcTimeLimit) {
      // Stop timer
      clearInterval(fmcTimerInterval);
      fmcTimerInterval = null;

      // Show time limit reached message
      alert("Time limit reached (60 minutes)");

      // Submit solution automatically
      submitFMCSolution();
      return;
    }

    // Update display
    updateFMCTimerDisplay();
  }, 1000); // Update every second
}

// Update FMC timer display
function updateFMCTimerDisplay() {
  // Ensure fmcElapsedTime is a valid number
  const elapsedTime = fmcElapsedTime || 0;

  // Format time
  const minutes = Math.floor(elapsedTime / 60000);
  const seconds = Math.floor((elapsedTime % 60000) / 1000);

  // Format as MM:SS
  const timeString = `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;

  // Update timer displays
  if (fmcTimerDisplay) {
    fmcTimerDisplay.textContent = timeString;
  }

  // Update keyboard timer if it exists
  const keyboardTimer = document.getElementById("fmc-keyboard-timer");
  if (keyboardTimer) {
    keyboardTimer.textContent = timeString;
  }
}

// Show visualization in modal
function showVisualization() {
  if (!fmcVisualizationModal || !fmcVisualizationContainer) return;

  // Show modal
  fmcVisualizationModal.classList.add("show");

  // Clear previous visualization
  fmcVisualizationContainer.innerHTML = "";

  // Check if TwistyPlayer is available
  if (!TwistyPlayer) {
    // Show fallback message when TwistyPlayer is not available
    const fallbackMessage = document.createElement("div");
    fallbackMessage.style.textAlign = "center";
    fallbackMessage.style.padding = "40px 20px";
    fallbackMessage.style.color = "var(--text-color, #333)";
    fallbackMessage.innerHTML = `
      <div style="font-size: 1.2rem; margin-bottom: 15px;">
        <i class="fas fa-exclamation-triangle" style="color: #f39c12; margin-right: 8px;"></i>
        Visualization Unavailable
      </div>
      <div style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 15px;">
        The 3D cube visualization is currently unavailable due to CDN issues.
      </div>
      <div style="font-size: 0.9rem; background: var(--bg-color-secondary, #f5f5f5); padding: 10px; border-radius: 4px; font-family: monospace;">
        Scramble: ${fmcScramble}
      </div>
    `;
    fmcVisualizationContainer.appendChild(fallbackMessage);
    return;
  }

  try {
    // Create a new twisty player with 2D visualization
    const twistyPlayer = new TwistyPlayer({
      puzzle: "3x3x3",
      alg: fmcScramble,
      background: "none",
      controlPanel: "none",
      visualization: "2D",
      experimentalSetupAnchor: "end", // Prevent linking to external editor
    });

    // Set the size using CSS
    twistyPlayer.style.width = "100%";
    twistyPlayer.style.height = "300px";

    // Add to container
    fmcVisualizationContainer.appendChild(twistyPlayer);
  } catch (error) {
    // Error creating TwistyPlayer

    // Show error message
    const errorMessage = document.createElement("div");
    errorMessage.style.textAlign = "center";
    errorMessage.style.padding = "40px 20px";
    errorMessage.style.color = "var(--text-color, #333)";
    errorMessage.innerHTML = `
      <div style="font-size: 1.2rem; margin-bottom: 15px;">
        <i class="fas fa-exclamation-triangle" style="color: #e74c3c; margin-right: 8px;"></i>
        Visualization Error
      </div>
      <div style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 15px;">
        Unable to load the 3D cube visualization.
      </div>
      <div style="font-size: 0.9rem; background: var(--bg-color-secondary, #f5f5f5); padding: 10px; border-radius: 4px; font-family: monospace;">
        Scramble: ${fmcScramble}
      </div>
    `;
    fmcVisualizationContainer.appendChild(errorMessage);
  }
}

// Submit FMC solution
function submitFMCSolution() {
  // Calculate final elapsed time before stopping timer
  if (fmcStartTime) {
    fmcElapsedTime = Date.now() - fmcStartTime;
  }

  // Stop timer
  if (fmcTimerInterval) {
    clearInterval(fmcTimerInterval);
    fmcTimerInterval = null;
  }

  // Get solution
  const solution = fmcSolutionInput ? fmcSolutionInput.value.trim() : "";
  fmcSolution = solution;

  // Validate solution
  validateFMCSolution(solution);

  // Show confirmation modal
  showFMCResultModal();
}

// Validate FMC solution
async function validateFMCSolution(solution) {
  // Check if solution is empty
  if (!solution) {
    fmcIsValid = false;
    fmcIsDNF = true;
    fmcDNFReason = "Empty solution";
    return;
  }

  try {
    // Check if time limit exceeded (60 minutes)
    if (fmcElapsedTime > fmcTimeLimit) {
      fmcIsValid = false;
      fmcIsDNF = true;
      fmcDNFReason = "Time limit exceeded";
      return;
    }

    // Check for invalid moves (like single-letter rotations X, Y, Z)
    const invalidMoves = ["X", "Y", "Z"];
    const moves = solution.split(/\s+/);
    for (const move of moves) {
      if (invalidMoves.includes(move.trim())) {
        fmcIsValid = false;
        fmcIsDNF = true;
        fmcDNFReason = `Invalid move: ${move}`;
        return;
      }
    }

    // Make sure all required libraries are loaded
    if (!Alg) {
      // Alg not loaded, loading now
      const algModule = await import("https://cdn.cubing.net/js/cubing/alg");
      Alg = algModule.Alg;
    }

    // Try to parse the solution
    try {
      const algObj = new Alg(solution);
      // Solution parsed successfully
    } catch (parseError) {
      // Solution does not parse correctly
      fmcIsValid = false;
      fmcIsDNF = true;
      fmcDNFReason = "Invalid notation: " + parseError.message;
      return;
    }

    // Calculate move count in HTM
    const moveCount = countHTM(solution);
    fmcMoveCount = moveCount;

    // Check move count for DNF
    if (moveCount > 80) {
      fmcIsValid = false;
      fmcIsDNF = true;
      fmcDNFReason = "Solution exceeds 80 moves";
      return;
    }

    // Create a validation URL for Twizzle
    const validationUrl = `https://alpha.twizzle.net/edit/?setup=${encodeURIComponent(
      fmcScramble
    )}&alg=${encodeURIComponent(
      "// Scramble\n" + fmcScramble + "\n// Solution\n" + solution
    )}&puzzle=3x3x3`;

    // Store the validation URL for reference
    fmcValidationUrl = validationUrl;

    // At this point, we've verified:
    // 1. The solution is not empty
    // 2. The time limit was not exceeded
    // 3. There are no invalid moves
    // 4. The solution parses correctly
    // 5. The move count is within the limit

    // We'll assume the solution is valid if it meets all these criteria
    // Solution is valid - meets all WCA requirements
    fmcIsValid = true;
    fmcIsDNF = false;
  } catch (error) {
    // Error in validation process
    fmcIsValid = false;
    fmcIsDNF = true;
    fmcDNFReason = "Validation error: " + error.message;
  }
}

// Count moves in Half Turn Metric (HTM)
function countHTM(solution) {
  // Remove comments
  const cleanSolution = solution.replace(/\/\/.*$/gm, "").trim();

  // Split into moves
  const moves = cleanSolution.split(/\s+/);

  // Filter out rotations (x, y, z) and count remaining moves
  const actualMoves = moves.filter((move) => {
    const baseLetter = move.charAt(0).toLowerCase();
    return !["x", "y", "z"].includes(baseLetter);
  });

  return actualMoves.length;
}

// Show FMC result modal
function showFMCResultModal() {
  // Get translations
  const translations = window.i18nModule?.translations || {};
  const fmcTranslations = translations.fmc || {};

  // Create modal
  const modal = document.createElement("div");
  modal.className = "fmc-result-modal";
  modal.id = "fmc-result-modal";

  // Create modal content
  const modalContent = document.createElement("div");
  modalContent.className = "fmc-result-modal-content";

  // Create header
  const modalHeader = document.createElement("div");
  modalHeader.className = "fmc-result-modal-header";

  // Create title
  const modalTitle = document.createElement("div");
  modalTitle.className = "fmc-result-modal-title";
  modalTitle.textContent = fmcTranslations.resultTitle || "FMC Result";

  // Create close button
  const closeButton = document.createElement("button");
  closeButton.className = "fmc-result-modal-close";
  closeButton.innerHTML = '<i class="fas fa-times"></i>';
  closeButton.addEventListener("click", () => {
    // Save result when closing with X button (as requested)
    saveFMCResultToList(resultData);

    // Automatically select FMC event to show the updated times list
    selectFMCEventInDropdown();

    modal.remove();

    // Reset FMC state
    resetFMC();
  });

  // Add title and close button to header
  modalHeader.appendChild(modalTitle);
  modalHeader.appendChild(closeButton);

  // Create result content
  const resultContent = document.createElement("div");
  resultContent.className = "fmc-result-content";

  // Add move count
  const moveCountElement = document.createElement("div");
  moveCountElement.className = "fmc-result-move-count";
  moveCountElement.textContent = fmcIsDNF
    ? "DNF"
    : `${fmcMoveCount} ${fmcTranslations.moves || "moves"}`;

  // Add time
  const timeElement = document.createElement("div");
  timeElement.className = "fmc-result-time";
  const minutes = Math.floor(fmcElapsedTime / 60000);
  const seconds = Math.floor((fmcElapsedTime % 60000) / 1000);
  timeElement.textContent = `${
    fmcTranslations.resultTime || "Time:"
  } ${minutes}:${seconds.toString().padStart(2, "0")}`;

  // Add validation message
  const validationElement = document.createElement("div");
  validationElement.className = "fmc-result-validation";

  if (fmcIsDNF) {
    validationElement.textContent = `DNF: ${fmcDNFReason}`;
    validationElement.classList.add("fmc-result-dnf");
  } else {
    validationElement.textContent =
      fmcTranslations.solutionAccepted || "Solution accepted";
    validationElement.classList.add("fmc-result-valid");
  }

  // Create validation details container
  const validationDetailsContainer = document.createElement("div");
  validationDetailsContainer.className = "fmc-validation-details";

  // Create validation details content
  let validationDetailsContent = "";

  if (fmcIsDNF) {
    validationDetailsContent = `
      <div class="fmc-validation-status fmc-validation-failed">
        <i class="fas fa-times-circle"></i> Solution is invalid
      </div>
      <div class="fmc-validation-reason">
        Reason: ${fmcDNFReason}
      </div>
    `;
  } else {
    validationDetailsContent = `
      <div class="fmc-validation-status fmc-validation-success">
        <i class="fas fa-check-circle"></i> Solution is valid
      </div>
      <div class="fmc-validation-details-item">
        <span class="fmc-validation-label">${
          fmcTranslations.moveCountLabel || "Move count:"
        }</span>
        <span class="fmc-validation-value">${fmcMoveCount} ${
      fmcTranslations.movesHTM || "moves (HTM)"
    }</span>
      </div>
      <div class="fmc-validation-details-item">
        <span class="fmc-validation-label">${
          fmcTranslations.timeUsedLabel || "Time used:"
        }</span>
        <span class="fmc-validation-value">${Math.floor(
          fmcElapsedTime / 60000
        )}:${Math.floor((fmcElapsedTime % 60000) / 1000)
      .toString()
      .padStart(2, "0")}</span>
      </div>
    `;
  }

  // No need to add a link to Twizzle here, we'll add a button instead

  // Set the validation details content
  validationDetailsContainer.innerHTML = validationDetailsContent;

  // Add a view on Twizzle button
  const twizzleButton = document.createElement("button");
  twizzleButton.className = "fmc-twizzle-button";
  twizzleButton.innerHTML = `<i class="fas fa-external-link-alt"></i> ${
    fmcTranslations.viewOnTwizzle || "View on Twizzle"
  }`;
  twizzleButton.addEventListener("click", () => {
    window.open(fmcValidationUrl, "_blank");
  });

  validationDetailsContainer.appendChild(twizzleButton);

  // Add validation element and details to result content
  resultContent.appendChild(validationElement);
  resultContent.appendChild(validationDetailsContainer);

  // Add solution
  const solutionElement = document.createElement("div");
  solutionElement.className = "fmc-result-solution";
  solutionElement.textContent = `${
    fmcTranslations.resultSolution || "Solution:"
  } ${fmcSolution}`;

  // Add elements to result content
  resultContent.appendChild(moveCountElement);
  resultContent.appendChild(timeElement);
  resultContent.appendChild(solutionElement);

  // Prepare result data (don't save yet) - make it available to both buttons
  const resultData = prepareFMCResult();

  // Add OK button
  const okButton = document.createElement("button");
  okButton.className = "fmc-result-ok-button";
  okButton.textContent = fmcTranslations.resultOk || "OK";
  okButton.addEventListener("click", () => {
    // Save result to times list when OK is pressed
    saveFMCResultToList(resultData);

    // Automatically select FMC event to show the updated times list
    selectFMCEventInDropdown();

    modal.remove();

    // Reset FMC state
    resetFMC();
  });

  // Add button container
  const buttonContainer = document.createElement("div");
  buttonContainer.className = "fmc-result-button-container";
  buttonContainer.appendChild(okButton);
  resultContent.appendChild(buttonContainer);

  // Add header and result content to modal content
  modalContent.appendChild(modalHeader);
  modalContent.appendChild(resultContent);

  // Add modal content to modal
  modal.appendChild(modalContent);

  // Add modal to body
  document.body.appendChild(modal);

  // Show modal immediately (no saving until OK is pressed)
  setTimeout(() => {
    modal.classList.add("show");
  }, 10);
}

// Prepare FMC result data (don't save to list yet)
function prepareFMCResult() {
  // Ensure we have a valid elapsed time
  let finalElapsedTime = fmcElapsedTime;

  // If elapsed time is 0 or null, calculate it from start time
  if ((!finalElapsedTime || finalElapsedTime === 0) && fmcStartTime) {
    finalElapsedTime = Date.now() - fmcStartTime;
  }

  // If we still don't have a valid time, use a minimal time
  if (!finalElapsedTime || finalElapsedTime <= 0) {
    finalElapsedTime = 1000; // 1 second minimum
  }

  // Return result data without saving
  return {
    scramble: fmcScramble,
    solution: fmcSolution,
    moveCount: fmcMoveCount,
    elapsedTime: finalElapsedTime,
    isDNF: fmcIsDNF,
    isValid: fmcIsValid,
    dnfReason: fmcDNFReason,
    validationUrl: fmcValidationUrl,
  };
}

// Save FMC result to times list (called when modal OK is pressed)
function saveFMCResultToList(resultData) {
  // Create result object that matches what the main timer expects for FMC
  const result = {
    time: resultData.isDNF ? Infinity : resultData.moveCount, // Use Infinity for DNF, move count for valid solves
    scramble: resultData.scramble,
    solution: resultData.solution,
    date: new Date().toISOString(),
    isDNF: resultData.isDNF,
    penalty: resultData.isDNF ? "DNF" : null,
    elapsedTime: resultData.elapsedTime, // This is what the times list uses for display
    // Add formatted result for display
    result: resultData.isDNF ? "DNF" : resultData.moveCount.toString(),
    // Add FMC-specific properties
    moveCount: resultData.moveCount,
    isValid: resultData.isValid,
    dnfReason: resultData.dnfReason,
    validationUrl: resultData.validationUrl,
    isFMC: true, // Mark as FMC result
    originalTime: resultData.isDNF ? resultData.moveCount : null, // For DNF cases
  };

  // Final FMC result object for times list

  // Get existing times from the global timesMap
  if (!window.timesMap) {
    // If timesMap doesn't exist, try to load it from localStorage
    try {
      const timesJson = localStorage.getItem("scTimer-times");
      window.timesMap = timesJson ? JSON.parse(timesJson) : {};
    } catch (error) {
      // Error loading times from localStorage
      window.timesMap = {};
    }
  }

  // Make sure the FMC event exists in timesMap
  if (!window.timesMap["333fm"]) {
    window.timesMap["333fm"] = [];
  }

  // Add new result to the beginning of the array (most recent first)
  window.timesMap["333fm"].unshift(result);

  // Save times to localStorage
  localStorage.setItem("scTimer-times", JSON.stringify(window.timesMap));

  // Make sure the current event is set to FMC
  if (typeof window.currentEvent !== "undefined") {
    window.currentEvent = "333fm";
  }

  // Force immediate UI updates
  // Update the times list in the UI
  if (typeof window.updateTimesList === "function") {
    window.updateTimesList();
  }

  // Update statistics
  if (typeof window.updateStatistics === "function") {
    window.updateStatistics();
  } else if (typeof window.updateStats === "function") {
    window.updateStats();
  }

  // Force multiple updates with delays to ensure UI refreshes
  setTimeout(() => {
    if (typeof window.updateTimesList === "function") {
      window.updateTimesList();
    }
    if (typeof window.updateStatistics === "function") {
      window.updateStatistics();
    }
  }, 100);

  // Update FMC-specific stats display
  setupFMCStatsDisplay();
}

// Automatically select FMC event in dropdown to show updated times list
function selectFMCEventInDropdown() {
  // Find the FMC event option in the dropdown
  const fmcOption = document.querySelector('.event-option[data-event="333fm"]');

  if (fmcOption) {
    // Simulate clicking on the FMC option to trigger the full event change process
    fmcOption.click();
  } else {
    // Fallback: manually trigger the event change process

    // Update current event variables
    if (typeof window.currentEvent !== "undefined") {
      window.currentEvent = "333fm";
    }

    // Update the event selector button text
    const eventSelectorBtn = document.querySelector(".event-selector-btn");
    const currentEventText = document.getElementById("current-event-text");

    if (eventSelectorBtn && currentEventText) {
      // Update button text to show FMC
      currentEventText.textContent = "3×3×3 Fewest Moves";

      // Update icon
      const currentEventIcon = eventSelectorBtn.querySelector(".cubing-icon");
      if (currentEventIcon) {
        currentEventIcon.className = "cubing-icon event-333fm";
      }
    }

    // Force update of times list and stats for FMC
    setTimeout(() => {
      if (typeof window.updateTimesList === "function") {
        window.updateTimesList();
      }
      if (typeof window.updateStatistics === "function") {
        window.updateStatistics();
      }
    }, 100);
  }
}

// Generate FMC scramble
export async function generateFMCScramble() {
  try {
    // Make sure cubing libraries are loaded
    if (!randomScrambleForEvent) {
      await loadCubingLibraries();

      // Double check that libraries loaded successfully
      if (!randomScrambleForEvent) {
        throw new Error("Failed to load cubing libraries");
      }
    }

    // Generate a proper FMC scramble
    const scramble = await randomScrambleForEvent("333fm");

    // Check if the scramble was generated successfully
    if (!scramble) {
      throw new Error("Failed to generate FMC scramble");
    }

    // Store the scramble in the FMC module
    fmcScramble = scramble.toString();

    // Update the global currentScramble if we're in FMC mode
    if (
      typeof window.currentScramble !== "undefined" &&
      window.currentEvent === "333fm"
    ) {
      window.currentScramble = fmcScramble;
    } else if (
      typeof window.currentScramble !== "undefined" &&
      window.currentEvent !== "333fm"
    ) {
      // Preserving main timer scramble
    }

    return fmcScramble;
  } catch (error) {
    // Error generating FMC scramble

    // No fallback - only WCA official scrambles
    throw new Error(
      "Failed to generate WCA official FMC scramble. Please refresh the page."
    );
  }
}

// Check if FMC attempt is in progress
export function isFMCInProgress() {
  return fmcAttemptInProgress;
}

// Reset FMC state
export function resetFMC() {
  // Stop timer if running
  if (fmcTimerInterval) {
    clearInterval(fmcTimerInterval);
    fmcTimerInterval = null;
  }

  // Reset state
  fmcAttemptInProgress = false;
  fmcScramble = "";
  fmcSolution = "";
  fmcMoveCount = 0;
  fmcIsValid = false;
  fmcIsDNF = false;
  fmcDNFReason = "";

  fmcValidationUrl = "";
  fmcIsReady = false;
  fmcIsLoading = false;

  // Reset timer variables
  fmcStartTime = null;
  fmcElapsedTime = 0;

  // Clean up interface
  cleanupFMCInterface();

  // Additional cleanup to ensure proper reset when switching events
  if (mainTimerElement) {
    // Check if we're still in FMC mode
    if (
      typeof window.currentEvent !== "undefined" &&
      window.currentEvent === "333fm"
    ) {
      // Show loading state for next attempt
      showFMCLoadingState();
      // Start initialization for next attempt with a delay to ensure cleanup is complete
      setTimeout(() => {
        initializeFMCAttempt();
      }, 200);
    } else {
      // Not in FMC mode, reset to normal timer
      mainTimerElement.innerHTML = ""; // Clear any HTML content
      // Use formatTime function for consistency with decimal places setting
      const decimalPlaces = parseInt(
        localStorage.getItem("scTimer-decimalPlaces") || "3"
      );
      if (decimalPlaces === 0) {
        mainTimerElement.textContent = "0";
      } else {
        mainTimerElement.textContent = "0." + "0".repeat(decimalPlaces);
      }
      mainTimerElement.classList.remove(
        "fmc-start-prompt",
        "fmc-loading",
        "fmc-error"
      );
      // Reset any inline styles that might have been set during FMC
      mainTimerElement.style.fontSize = "";
      mainTimerElement.style.opacity = "";
    }

    // Make sure timer is visible
    mainTimerElement.style.display = "block";
    mainTimerElement.style.visibility = "visible";
    mainTimerElement.style.opacity = "1";
    mainTimerElement.style.color = ""; // Reset color
  }

  // Make sure scramble is visible
  if (mainScrambleElement) {
    mainScrambleElement.style.visibility = "visible";

    // Clear the scramble text to ensure it's updated
    const scrambleText = document.getElementById("scramble-text");
    if (scrambleText) {
      scrambleText.textContent = "";
      scrambleText.classList.add("hidden");
    }

    // Clear any scramble class that might be set
    mainScrambleElement.className = "scramble";

    // Add the current event class back
    if (typeof window.currentEvent !== "undefined") {
      mainScrambleElement.classList.add(`event-${window.currentEvent}`);
    }
  }

  // Reset the current scramble in the main timer
  if (typeof window.currentScramble !== "undefined") {
    window.currentScramble = "";
  }

  // Reset the scramble generation flag
  if (typeof window.isGeneratingScramble !== "undefined") {
    window.isGeneratingScramble = false;
  }

  // Force a refresh of the visualization to ensure it's properly reset
  if (typeof window.updateVisualization === "function") {
    setTimeout(window.updateVisualization, 100);
  }
}

// Completely remove all FMC elements from the DOM
export function nukeAllFMCElements() {
  // List of all possible FMC element IDs
  const fmcElementIds = [
    "fmc-solution-container",
    "fmc-keyboard-container",
    "fmc-result-modal",
    "fmc-visualization-modal",
    "fmc-timer-display",
    "fmc-move-count",
    "fmc-solution-input",
    "fmc-validation-message",
    "fmc-submit-button",
    "fmc-keyboard",
    "fmc-visualization-container",
  ];

  // Remove all FMC elements by ID
  fmcElementIds.forEach((id) => {
    const element = document.getElementById(id);
    if (element) {
      element.remove();
    }
  });

  // Remove any elements with FMC classes
  const fmcClasses = [
    "fmc-button",
    "fmc-key",
    "fmc-start-prompt",
    "fmc-timer",
    "fmc-solution",
  ];

  fmcClasses.forEach((className) => {
    const elements = document.getElementsByClassName(className);

    // Convert HTMLCollection to Array to avoid issues with live collections
    Array.from(elements).forEach((element) => {
      element.remove();
    });
  });

  // Remove any elements with FMC in the ID (catch-all)
  const allElements = document.querySelectorAll('[id*="fmc"]');

  allElements.forEach((element) => {
    element.remove();
  });

  // Reset the timer element
  const timerElement = document.getElementById("timer");
  if (timerElement) {
    // Use formatTime function for consistency with decimal places setting
    const decimalPlaces = parseInt(
      localStorage.getItem("scTimer-decimalPlaces") || "3"
    );
    if (decimalPlaces === 0) {
      timerElement.textContent = "0";
    } else {
      timerElement.textContent = "0." + "0".repeat(decimalPlaces);
    }
    timerElement.style.display = "block";
    timerElement.style.fontSize = "";
    timerElement.style.color = "";
    timerElement.classList.remove("fmc-start-prompt");
  }

  // Reset the scramble element
  const scrambleElement = document.getElementById("scramble");
  if (scrambleElement) {
    scrambleElement.style.visibility = "visible";
  }

  // Reset visualization container
  const visualizationContainer = document.getElementById(
    "visualization-container"
  );
  if (visualizationContainer) {
    visualizationContainer.style.display = "block";
    visualizationContainer.classList.remove("hidden");
  }

  // Reset stats container
  const statsContainer = document.querySelector(".stats-container");
  if (statsContainer) {
    statsContainer.style.display = "block";
    statsContainer.classList.remove("hidden");
  }

  // Re-enable checkboxes
  const checkboxes = ["show-stats", "show-visualization", "show-fmc-keyboard"];
  checkboxes.forEach((id) => {
    const checkbox = document.getElementById(id);
    if (checkbox) {
      checkbox.disabled = false;
      if (
        checkbox.parentElement &&
        checkbox.parentElement.classList.contains("disabled")
      ) {
        checkbox.parentElement.classList.remove("disabled");
      }
    }
  });

  // FMC element nuking complete
}

// Clean up FMC interface
function cleanupFMCInterface() {
  // Remove solution interface
  const solutionContainer = document.getElementById("fmc-solution-container");
  if (solutionContainer) {
    solutionContainer.remove();
  }

  // Remove keyboard
  if (bottomContainer) {
    bottomContainer.remove();
    bottomContainer = null;
  }

  // Remove any FMC keyboard container that might still be in the DOM
  const keyboardContainer = document.getElementById("fmc-keyboard-container");
  if (keyboardContainer) {
    keyboardContainer.remove();
  }

  // Remove any FMC result modal that might be open
  const resultModal = document.getElementById("fmc-result-modal");
  if (resultModal) {
    resultModal.remove();
  }

  // Remove any FMC visualization modal that might be open
  const visualizationModal = document.getElementById("fmc-visualization-modal");
  if (visualizationModal) {
    visualizationModal.remove();
  }

  // Show timer again and reset its style and text
  if (mainTimerElement) {
    mainTimerElement.style.display = "block"; // Explicitly set to block

    // Check if we're still in FMC mode - if so, keep the prompt
    if (
      typeof window.currentEvent !== "undefined" &&
      window.currentEvent === "333fm"
    ) {
      // Keep FMC prompt for next attempt
      const translations = window.i18nModule?.translations || {};
      const fmcTranslations = translations.fmc || {};

      mainTimerElement.textContent =
        fmcTranslations.pressToStart || "Press space to start FMC attempt";
      mainTimerElement.classList.add("fmc-start-prompt");
    } else {
      // Not in FMC mode, remove prompt
      mainTimerElement.classList.remove("fmc-start-prompt");
      // Use formatTime function for consistency with decimal places setting
      const decimalPlaces = parseInt(
        localStorage.getItem("scTimer-decimalPlaces") || "3"
      );
      if (decimalPlaces === 0) {
        mainTimerElement.textContent = "0";
      } else {
        mainTimerElement.textContent = "0." + "0".repeat(decimalPlaces);
      }
    }
    mainTimerElement.style.fontSize = ""; // Reset font size
    mainTimerElement.style.color = ""; // Reset color

    // Reset the timer text to original or use consistent format instead of FMC prompt
    if (mainTimerElement.dataset.originalText) {
      mainTimerElement.textContent = mainTimerElement.dataset.originalText;
    } else {
      // Use formatTime function for consistency with decimal places setting
      const decimalPlaces = parseInt(
        localStorage.getItem("scTimer-decimalPlaces") || "3"
      );
      if (decimalPlaces === 0) {
        mainTimerElement.textContent = "0";
      } else {
        mainTimerElement.textContent = "0." + "0".repeat(decimalPlaces);
      }
    }
  }

  // Restore stats container
  if (mainStatsContainer && originalStatsContent) {
    mainStatsContainer.style.display = "block"; // Explicitly set to block
    mainStatsContainer.classList.remove("hidden");
    mainStatsContainer.innerHTML = originalStatsContent;
  }

  // Restore visualization box
  if (mainVisualizationBox) {
    // Restore original display style
    mainVisualizationBox.style.display = "block"; // Explicitly set to block
    mainVisualizationBox.classList.remove("hidden");
    mainVisualizationBox.style.width = "";
    mainVisualizationBox.style.gridColumn = "";

    // Restore original content if available
    if (originalVisualizationContent) {
      mainVisualizationBox.innerHTML = originalVisualizationContent;
    }
  }

  // Restore visualization container if it exists
  if (mainVisualizationContainer) {
    // Restore original display style if saved
    if (mainVisualizationContainer.dataset.originalDisplay) {
      mainVisualizationContainer.style.display =
        mainVisualizationContainer.dataset.originalDisplay;
    } else {
      // Default to block if no original style was saved
      mainVisualizationContainer.style.display = "block";
    }

    // Make sure it's visible
    mainVisualizationContainer.classList.remove("hidden");

    // Restore any existing twisty player
    const existingTwistyPlayer =
      mainVisualizationContainer.querySelector("twisty-player");
    if (existingTwistyPlayer) {
      // Restore original display style if saved
      if (existingTwistyPlayer.dataset.originalDisplay) {
        existingTwistyPlayer.style.display =
          existingTwistyPlayer.dataset.originalDisplay;
      } else {
        // Default to block if no original style was saved
        existingTwistyPlayer.style.display = "block";
      }
    }
  }

  // Force a refresh of the visualization to ensure it's properly reset
  if (typeof window.updateVisualization === "function") {
    setTimeout(window.updateVisualization, 100);
  }

  // Make scramble visible again
  if (mainScrambleElement) {
    mainScrambleElement.style.visibility = "visible";
  }

  // Make sure the timer container is clean
  const timerContainer = document.querySelector(".timer-container");
  if (timerContainer) {
    // Remove any FMC-related elements from the timer container
    const fmcElements = timerContainer.querySelectorAll("[id^='fmc-']");
    fmcElements.forEach((element) => {
      element.remove();
    });
  }

  // FMC interface cleanup complete
}

// Restart FMC with a new scramble
export function restartFMCWithNewScramble() {
  // Reset FMC state
  resetFMC();

  // Show loading state
  showFMCLoadingState();

  // Start the initialization process (which will generate scramble and show ready state)
  initializeFMCAttempt();
}

// Update FMC keyboard label state
export function updateFMCKeyboardLabelState() {
  const showFMCKeyboardLabel = document.getElementById(
    "show-fmc-keyboard-label"
  );
  const showFMCKeyboardCheckbox = document.getElementById("show-fmc-keyboard");

  if (showFMCKeyboardLabel && showFMCKeyboardCheckbox) {
    // Enable/disable based on current event
    const isFMCEvent = window.currentEvent === "333fm";

    if (isFMCEvent) {
      showFMCKeyboardLabel.classList.remove("disabled");
      showFMCKeyboardCheckbox.disabled = false;
    } else {
      showFMCKeyboardLabel.classList.add("disabled");
      showFMCKeyboardCheckbox.disabled = true;
    }
  }
}
